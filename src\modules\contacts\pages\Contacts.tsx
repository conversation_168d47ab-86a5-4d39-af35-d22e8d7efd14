import Background from "@/components/backgound";
import { useTranslations } from "next-intl";
// import VK from "@shared/assets/vk";
// import Telegram from "@shared/assets/telegram";
// import Youtube from "@shared/assets/youtube";

const Contacts = () => {
  const t = useTranslations("contacts");
  return (
    <div className="relative min-h-[80vh] md:pt-[200px] pt-[120px] md:pb-[70px] pb-[100px] lg:px-[50px] px-5 flex flex-col md:gap-[40px] gap-4">
      <Background />
      <h2 className="md:text-[89px] text-[46px] md:leading-[86px] leading-[46px] font-semibold tracking-[-4px] text-centerw">
        {t("title")}
      </h2>
      <div className="flex flex-col items-end">
        <div className="flex flex-col md:gap-[175px] gap-[100px] lg:w-[60%]">
          <p className="md:text-[22px] text-[16px] leading-[130%]">
            {t("description")}
          </p>

          <div className="flex md:justify-between md:flex-row flex-col md:gap-2 gap-[50px] md:pr-[75px]">
            <div className="flex flex-col gap-[50px]">
              <div className="flex flex-col md:gap-5 gap-[14px]">
                <label
                  className="md:text-[22px] text-[16px] font-medium leading-[100%]"
                  htmlFor="phoneNumber"
                >
                  {t("phoneNumber")}
                </label>
                <p
                  id="phoneNumber"
                  className="md:text-[32px] text-[28px] font-semibold text-[#B447FF]"
                >
                  +996 (998) 999 364
                </p>
              </div>

              {/* <div className="flex flex-col md:gap-5 gap-[14px]">
                <label
                  className="md:text-[22px] text-[16px] font-medium leading-[100%]"
                  htmlFor="email"
                >
                  E-Mail
                </label>
                <p
                  id="email"
                  className="md:text-[32px] text-[28px] font-semibold text-[#B447FF]"
                >
                  <EMAIL>
                </p>
              </div> */}
            </div>
            {/* <div className="flex flex-col md:gap-5 gap-[14px]">
              <label
                className="md:text-[22px] text-[16px] font-medium leading-[100%] text-nowrap"
                htmlFor="email"
              >
                {t("usInSocialNets")}
              </label>
              <div className="flex gap-4">
                <a href="#">
                  <VK />
                </a>
                <a href="#">
                  <Youtube />
                </a>
                <a href="#">
                  <Telegram />
                </a>
              </div>
            </div> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Contacts;
