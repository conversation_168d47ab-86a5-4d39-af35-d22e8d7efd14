import Image from "next/image";
import Typography from "@/components/typography";
import Background from "@/components/backgound";
import Tag from "@/components/tag";
import Card from "./card";
import case1 from "@our-services/assets/cases/1.svg";
import case2 from "@our-services/assets/cases/2.svg";
// import case3 from "@our-services/assets/cases/3.svg";
import spiral from "@our-services/assets/shapes/spiral.svg";
import { setImageToText } from "@/shared/functions/setImageToText";
import Companies from "@/modules/home/<USER>/TrustUs/Companies";
import { getServerTheme } from "@/shared/functions/getServerTheme";
import { getTranslations } from "next-intl/server";

const Header = async () => {
  const theme = await getServerTheme();
  const t = await getTranslations("ourServices");

  return (
    <div className="relative lg:pt-[210px] pt-[110px] pb-[100px]">
      <Background />

      <div className="flex flex-col gap-[70px] lg:px-[50px] px-5">
        <div className="flex flex-col items-center gap-5 max-w-[1000px] m-auto">
          <Tag>{t("preTitle")}</Tag>
          <Typography variant="h3" className="text-center">
            {setImageToText(t("title"), "Услуги,", spiral, {
              width: 102,
              height: 102,
            })}
          </Typography>
        </div>

        <div className="grid md:grid-cols-4 sm:grid-cols-2 grid-cols-1 md:grid-rows-1 grid-rows-2 gap-4">
          <Card>
            <div className="flex flex-col items-end gap-[26px] w-full">
              <Image src={case1} alt="Case 1" />

              <div className="flex flex-col gap-[28px] w-full">
                <h5 className="md:text-[64px] text-[57px] font-semibold md:leading-[46px] leading-[41px]">
                  4
                </h5>
                <p className="text-[16px] font-medium leading-[130%]">
                  {t("projectCount")}
                </p>
              </div>
            </div>
          </Card>
          <Card className="md:col-[2_/_4] sm:col-[1_/_3] cols-[1_/_2] md:row-[1_/_2] row-[2_/_3]">
            <div className="flex flex-col gap-[24px]">
              <Image src={case2} alt="Case 1" />

              <p className="text-[16px] font-medium">{t("successCase")}</p>

              <div className="flex flex-col gap-1">
                <h6 className="md:text-[18px] text-[16px] font-bold">
                  Айгерим Бекова
                </h6>
              </div>
            </div>
          </Card>
          <Card>
            <div className="flex flex-col justify-between sm:h-full h-[200px]">
              <div className="flex flex-col gap-[10px]">
                <h5 className="text-[36px] font-medium leading-[100%]">
                  {t("clients.title")}
                </h5>
                <p className="text-[16px] font-medium leading-[130%]">
                  {t("clients.description")}
                </p>
              </div>

              <Companies isRow serverTheme={theme} />
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Header;
