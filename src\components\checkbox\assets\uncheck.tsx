"use client";

import { useCheckMount } from "@/shared/hooks/useCheckMount";
import { useTheme } from "next-themes";

const Uncheck = () => {
  const { theme } = useTheme();
  const isMounted = useCheckMount();

  if (!isMounted) return null;
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.18404 20.564L5.52453 19.8958L5.18404 20.564ZM3.43597 18.816L4.10423 18.4755L3.43597 18.816ZM20.564 18.816L19.8958 18.4755L20.564 18.816ZM18.816 20.564L18.4755 19.8958L18.816 20.564ZM18.816 3.43597L18.4755 4.10423L18.816 3.43597ZM20.564 5.18404L19.8958 5.52453L20.564 5.18404ZM5.18404 3.43597L5.52453 4.10423L5.18404 3.43597ZM3.43597 5.18404L4.10423 5.52453L3.43597 5.18404ZM9.4 3.75H14.6V2.25H9.4V3.75ZM20.25 9.4V14.6H21.75V9.4H20.25ZM14.6 20.25H9.4V21.75H14.6V20.25ZM3.75 14.6V9.4H2.25V14.6H3.75ZM9.4 20.25C8.26752 20.25 7.46326 20.2494 6.83388 20.198C6.21325 20.1473 5.829 20.0509 5.52453 19.8958L4.84355 21.2323C5.39472 21.5131 5.99834 21.6347 6.71173 21.693C7.41637 21.7506 8.29227 21.75 9.4 21.75V20.25ZM2.25 14.6C2.25 15.7077 2.24942 16.5836 2.30699 17.2883C2.36527 18.0017 2.48688 18.6053 2.76772 19.1565L4.10423 18.4755C3.94909 18.171 3.85271 17.7867 3.80201 17.1661C3.75058 16.5367 3.75 15.7325 3.75 14.6H2.25ZM5.52453 19.8958C4.913 19.5842 4.41582 19.087 4.10423 18.4755L2.76772 19.1565C3.22312 20.0502 3.94978 20.7769 4.84355 21.2323L5.52453 19.8958ZM20.25 14.6C20.25 15.7325 20.2494 16.5367 20.198 17.1661C20.1473 17.7867 20.0509 18.171 19.8958 18.4755L21.2323 19.1565C21.5131 18.6053 21.6347 18.0017 21.693 17.2883C21.7506 16.5836 21.75 15.7077 21.75 14.6H20.25ZM14.6 21.75C15.7077 21.75 16.5836 21.7506 17.2883 21.693C18.0017 21.6347 18.6053 21.5131 19.1565 21.2323L18.4755 19.8958C18.171 20.0509 17.7867 20.1473 17.1661 20.198C16.5367 20.2494 15.7325 20.25 14.6 20.25V21.75ZM19.8958 18.4755C19.5842 19.087 19.087 19.5842 18.4755 19.8958L19.1565 21.2323C20.0502 20.7769 20.7769 20.0502 21.2323 19.1565L19.8958 18.4755ZM14.6 3.75C15.7325 3.75 16.5367 3.75058 17.1661 3.80201C17.7867 3.85271 18.171 3.94909 18.4755 4.10423L19.1565 2.76772C18.6053 2.48688 18.0017 2.36527 17.2883 2.30699C16.5836 2.24942 15.7077 2.25 14.6 2.25V3.75ZM21.75 9.4C21.75 8.29227 21.7506 7.41637 21.693 6.71173C21.6347 5.99834 21.5131 5.39472 21.2323 4.84355L19.8958 5.52453C20.0509 5.829 20.1473 6.21325 20.198 6.83388C20.2494 7.46326 20.25 8.26752 20.25 9.4H21.75ZM18.4755 4.10423C19.087 4.41582 19.5842 4.913 19.8958 5.52453L21.2323 4.84355C20.7769 3.94978 20.0502 3.22312 19.1565 2.76772L18.4755 4.10423ZM9.4 2.25C8.29227 2.25 7.41637 2.24942 6.71173 2.30699C5.99834 2.36527 5.39472 2.48688 4.84355 2.76772L5.52453 4.10423C5.829 3.94909 6.21325 3.85271 6.83388 3.80201C7.46326 3.75058 8.26752 3.75 9.4 3.75V2.25ZM3.75 9.4C3.75 8.26752 3.75058 7.46326 3.80201 6.83388C3.85271 6.21325 3.94909 5.829 4.10423 5.52453L2.76772 4.84355C2.48688 5.39472 2.36527 5.99834 2.30699 6.71173C2.24942 7.41637 2.25 8.29227 2.25 9.4H3.75ZM4.84355 2.76772C3.94978 3.22312 3.22312 3.94978 2.76772 4.84355L4.10423 5.52453C4.41582 4.913 4.913 4.41582 5.52453 4.10423L4.84355 2.76772Z"
        fill={theme === "dark" ? "white" : "black"}
      />
    </svg>
  );
};

export default Uncheck;
