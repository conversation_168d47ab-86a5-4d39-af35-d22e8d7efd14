"use client";

import { useCheckMount } from "@/shared/hooks/useCheckMount";
import { useTheme } from "next-themes";
import React from "react";

const Telegram = () => {
  const { theme } = useTheme();
  const isMounted = useCheckMount();

  if (!isMounted) return null;

  return (
    <svg
      width="41"
      height="41"
      viewBox="0 0 41 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="1"
        y="1"
        width="39"
        height="39"
        rx="19.5"
        stroke={theme === "dark" ? "#ffffff" : "#181818"}
      />
      <path
        d="M28.5 13.1022L25.4946 28.7923C25.4946 28.7923 25.0741 29.8801 23.9189 29.3584L16.9846 23.8526L16.9524 23.8364C17.8891 22.9654 25.1524 16.2027 25.4698 15.8961C25.9613 15.4214 25.6562 15.1387 25.0856 15.4974L14.3568 22.553L10.2176 21.1108C10.2176 21.1108 9.56626 20.8708 9.50359 20.3491C9.4401 19.8265 10.2391 19.5439 10.2391 19.5439L27.1131 12.6889C27.1131 12.6889 28.5 12.0579 28.5 13.1022Z"
        fill={theme === "dark" ? "#ffffff" : "#181818"}
      />
    </svg>
  );
};

export default Telegram;
