FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copy package.json and yarn.lock (if available), as well as any yarn configuration files
COPY package.json yarn.lock* .yarnrc* ./
# Install dependencies with Yarn using a frozen lockfile to ensure reproducible installs
RUN yarn install --frozen-lockfile

# Rebuild the source code only when needed
FROM base AS builder

ARG NEXT_PUBLIC_BASE_URL

ENV NEXT_PUBLIC_BASE_URL=$NEXT_PUBLIC_BASE_URL

WORKDIR /app
# Copy installed node_modules from deps stage
COPY --from=deps /app/node_modules ./node_modules
# Copy all source files
COPY . .

# Build the project if yarn.lock exists, otherwise exit with error
RUN if [ -f yarn.lock ]; then yarn build; else echo "yarn.lock not found." && exit 1; fi

# Production image: copy all build files
FROM node:18-alpine AS runner
WORKDIR /app

ENV NODE_ENV=production
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy package.json so that the start script can run
COPY --from=builder --chown=nextjs:nodejs /app/package.json ./
# Copy dependencies from deps stage
COPY --from=deps --chown=nextjs:nodejs /app/node_modules ./node_modules
# Copy build output and static assets
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

USER nextjs

EXPOSE 3000
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

CMD ["yarn", "start"]
