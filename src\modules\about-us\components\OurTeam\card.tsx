import { Employee } from "./types/TeamProfile";
import { ThemedImage } from "./themed-image";

const Card = ({
  name,
  position,
  skills,
  light_image,
  dark_image,
}: Employee) => {
  return (
    <div className="min-w-[323px] h-[360px] rounded-[26px] overflow-hidden relative cursor-pointer border-solid border-[1px] border-[#1818181A] group">
      <ThemedImage dark_image={dark_image} light_image={light_image} />

      <div className="flex flex-col justify-end gap-0.5 relative z-10 h-full w-full p-4 text-white bg-[linear-gradient(180.25deg,rgba(0,0,0,0)_71.57%,#000000_99.78%)]">
        <h6 className="font-bold text-[22px] leading-[130%]">{name}</h6>
        <span className="font-semibold text-[16px] leading-[130%]">
          {position}
        </span>
      </div>

      <div className="p-5 flex flex-col justify-between w-full h-full bg-[#F0F0F0] absolute z-10 top-[100%] left-0 transition-all duration-500 group-hover:top-0">
        <h6 className="font-bold text-[22px] leading-[130%] text-[#181818]">
          {name}
        </h6>

        <div className="flex flex-col gap-[6px]">
          <span className="font-bold text-[16px] leading-[130%] text-[#181818]">
            {position}
          </span>
          <span className="text-[16px] leading-[130%] font-medium text-[#181818]">
            {skills.map((item) => item.name).join(", ")}
          </span>
        </div>
      </div>
    </div>
  );
};

export default Card;
