"use client";

import { useCheckMount } from "@/shared/hooks/useCheckMount";
import { useTheme } from "next-themes";

const Globe = () => {
  const { theme } = useTheme();
  const isMounted = useCheckMount();

  if (!isMounted) return null;

  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 17.25C13.5563 17.25 17.25 13.5563 17.25 9C17.25 4.44365 13.5563 0.75 9 0.75M9 17.25C4.44365 17.25 0.75 13.5563 0.75 9C0.75 4.44365 4.44365 0.75 9 0.75M9 17.25C10.6569 17.25 12 13.5563 12 9C12 4.44365 10.6569 0.75 9 0.75M9 17.25C7.34315 17.25 6 13.5563 6 9C6 4.44365 7.34315 0.75 9 0.75M1.5 6H16.5M1.5 11.25H16.5"
        stroke={theme === "light" ? "#090909" : "#ffffff"}
        strokeWidth="1.3"
        strokeLinecap="round"
      />
    </svg>
  );
};

export default Globe;
