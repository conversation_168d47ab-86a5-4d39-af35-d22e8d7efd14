import { ParseRule } from './StatisticParser';

// Predefined rule sets for common use cases
export const STATISTIC_RULES = {
  // Highlights numbers with + or % (300+, 99.9%)
  NUMBERS_WITH_SUFFIX: {
    type: 'highlight' as const,
    pattern: /\d+(?:[.,]\d+)?[+%]/g,
  },

  // Highlights rating patterns (4.8 из 5, 4.8 out of 5)
  RATINGS: {
    type: 'highlight' as const,
    pattern: /\d+(?:[.,]\d+)?\s+(?:из|out\s+of)\s+\d+/gi,
  },

  // Highlights specific words
  WORDS: (words: string[]) => ({
    type: 'highlight' as const,
    words,
  }),

  // Highlights time patterns (24/7)
  TIME_PATTERNS: {
    type: 'highlight' as const,
    pattern: /\d+\/\d+/g,
  },

  // Highlights percentages
  PERCENTAGES: {
    type: 'highlight' as const,
    pattern: /\d+(?:[.,]\d+)?%/g,
  },

  // Highlights large numbers with k+ suffix
  LARGE_NUMBERS: {
    type: 'highlight' as const,
    pattern: /\d+к?\+/gi,
  },
} as const;

// Common rule combinations
export const RULE_COMBINATIONS = {
  // For general statistics (numbers with +, %, ratings)
  GENERAL_STATS: [
    STATISTIC_RULES.NUMBERS_WITH_SUFFIX,
    STATISTIC_RULES.RATINGS,
    STATISTIC_RULES.PERCENTAGES,
    STATISTIC_RULES.LARGE_NUMBERS,
  ] as ParseRule[],

  // For highlighting specific words only
  WORD_HIGHLIGHT: (words: string[]) => [
    STATISTIC_RULES.WORDS(words),
  ] as ParseRule[],

  // For time-based statistics
  TIME_STATS: [
    STATISTIC_RULES.TIME_PATTERNS,
    STATISTIC_RULES.PERCENTAGES,
  ] as ParseRule[],
} as const;
