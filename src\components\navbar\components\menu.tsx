import MenuIcon from "./menuIcon";
import line1 from "../assets/line1.svg";
import line2 from "../assets/line2.svg";
import Image from "next/image";
import { PAGES } from "@/shared/consts/pages";
import { useState } from "react";
import Close from "@/shared/assets/close";
import { usePathname } from "next/navigation";
import Link from "next/link";

const Menu = () => {
  const pathname = usePathname();
  const [isOpenMenu, setIsOpenMenu] = useState<boolean>(false);
  const pages = PAGES();

  return (
    <div className="relative lg:hidden flex">
      <button onClick={() => setIsOpenMenu(true)}>
        <MenuIcon />
      </button>

      {isOpenMenu ? (
        <div className="fixed right-0 top-0 sm:w-[350px] w-full h-full bg-white dark:bg-black shadow-2xl flex flex-col gap-[100px]">
          <div className="px-[20px] pt-[30px] flex justify-end">
            <button onClick={() => setIsOpenMenu(false)}>
              <Close />
            </button>
          </div>

          <div className="flex flex-col items-center gap-[40px]">
            {pages.map((item) => (
              <ul
                key={item.path}
                className={`text-[36px] leading-[100%] font-semibold ${
                  pathname === item.path ? "text-[#B344FF]" : ""
                }`}
              >
                <li>
                  <Link href={item.path} onClick={() => setIsOpenMenu(false)}>
                    {pathname === item.path ? (
                      <span className="bg-[#B344FF] w-[6px] h-[6px] rounded-full" />
                    ) : null}
                    {item.name}
                  </Link>
                </li>
              </ul>
            ))}
          </div>

          <div className="absolute top-0 left-0 w-full h-full flex pointer-events-none">
            <Image src={line1} alt="Line 1" />
            <Image src={line2} alt="Line 2" />
          </div>
        </div>
      ) : null}
    </div>
  );
};

export default Menu;
