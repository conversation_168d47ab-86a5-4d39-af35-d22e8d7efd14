import React from "react";

type Props = {
  title: string;
  includes: string[];
  description: string;
};

const Card = ({ title, description, includes }: Props) => {
  return (
    <div className="flex flex-col md:gap-[30px] gap-4 bg-[#F3F5F8] dark:bg-[#2A2A2A] border-solid border-[1px] border-[#1818181A] rounded-[30px]">
      <div className="md:px-[30px] px-4 md:py-[30.5px] py-[26px] border-b-[#1818181A] border-b-[1px] border-solid">
        <h5 className="md:text-[26px] text-[22px] font-bold">{title}</h5>
      </div>

      <ul className="flex flex-col gap-[10px] md:px-[30px] px-4">
        {includes.map((item) => (
          <li key={item} className="flex items-center gap-3">
            <span className="bg-[#181818] dark:bg-white min-w-[7px] h-[7px] rounded-full" />
            <span className="md:text-[18px] text-[16px] font-medium leading-[130%]">
              {item}
            </span>
          </li>
        ))}
      </ul>

      <div className="flex-1 flex items-end md:px-[30px] px-4 mt-[30px] md:pb-[30px] pb-4">
        <p className="md:text-[18px] text-[16px] font-bold text-[#1A1A1A] dark:text-white">
          {description}
        </p>
      </div>
    </div>
  );
};

export default Card;
