{"name": "nexlinkpro", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.800.0", "@aws-sdk/s3-request-presigner": "^3.800.0", "@heroui/button": "^2.2.19", "@heroui/modal": "^2.2.16", "@heroui/react": "^2.8.0-beta.2", "@heroui/skeleton": "^2.2.12", "@heroui/system": "2.4.15", "@heroui/theme": "2.4.15", "@heroui/toast": "^2.0.9", "@plaiceholder/next": "^3.0.0", "@tanstack/react-query": "^5.77.2", "axios": "^1.9.0", "critters": "^0.0.25", "framer-motion": "12.12.2", "next": "15.2.4", "next-intl": "^4.0.2", "next-themes": "^0.4.6", "plaiceholder": "^3.0.0", "react": "19.1.0", "react-dom": "19.1.0", "swr": "^2.3.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4", "typescript": "^5"}}