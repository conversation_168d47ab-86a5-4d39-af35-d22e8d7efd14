// shared/hooks/useTypewriter.ts
import { useEffect, useState } from "react";

export function useTypewriter(
  text: string,
  speed = 50
): { displayed: string; isTyping: boolean } {
  const [displayed, setDisplayed] = useState("");
  const [isTyping, setIsTyping] = useState(false);

  useEffect(() => {
    let idx = 0;
    setDisplayed("");
    setIsTyping(true);

    const step = () => {
      idx++;
      if (idx <= text.length) {
        // Берём префикс длины idx
        setDisplayed(text.slice(0, idx));
        // Запланировать следующий шаг
        setTimeout(step, speed);
      } else {
        setIsTyping(false);
      }
    };

    // Стартуем
    step();

    // На очистку очистим флаг и обрежем дальнейшие таймауты
    return () => {
      setIsTyping(false);
    };
  }, [text, speed]);

  return { displayed, isTyping };
}
