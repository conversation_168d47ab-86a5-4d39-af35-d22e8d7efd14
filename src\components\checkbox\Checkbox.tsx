"use client";

import { useEffect, useState } from "react";
import Check from "./assets/check";
import Uncheck from "./assets/uncheck";

type Props = {
  value?: boolean;
  onChange?: (value: boolean) => void;
};

const Checkbox = ({ value, onChange }: Props) => {
  const [isChecked, setIsChecked] = useState<boolean>(false);

  const handleCheck = () => {
    setIsChecked((prev) => {
      if (onChange) {
        onChange(prev);
      }

      return !prev;
    });
  };

  useEffect(() => {
    if (!value && value === isChecked) return;

    setIsChecked(false);
  }, [value, isChecked]);

  return (
    <button
      className="min-w-[24px] h-[24px] flex items-center justify-center"
      onClick={handleCheck}
    >
      {isChecked ? <Check /> : <Uncheck />}
    </button>
  );
};

export default Checkbox;
