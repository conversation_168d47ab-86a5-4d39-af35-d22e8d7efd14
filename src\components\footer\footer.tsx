"use client";

import Image from "next/image";
import nexlink from "./assets/nexlinkpro.svg";
import logo from "@shared/assets/logo.svg";
import { useTranslations } from "next-intl";
import Typography from "../typography";
import { PAGES } from "@shared/consts/pages";
import Input from "../input";
import Button from "../button";
import VK from "@shared/assets/vk";
import Telegram from "@shared/assets/telegram";
import Youtube from "@shared/assets/youtube";
import { useState } from "react";
import { FeedbackPayload } from "@shared/types";
import { sendForm } from "@/shared/api";
import { addToast } from "@heroui/toast";
import { Spinner } from "@heroui/react";
import Link from "next/link";

const Footer = () => {
  const t = useTranslations("common");
  const [errors, setErrors] = useState<Partial<
    Record<keyof FeedbackPayload, string>
  > | null>(null);
  const [feedback, setFeedback] = useState<FeedbackPayload>({
    name: "",
    email: "",
    phone_number: "",
    message: "",
  });
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleFeedbackChanges = (
    e: React.ChangeEvent<HTMLInputElement>,
    fieldname: keyof FeedbackPayload
  ) => {
    setFeedback((prev) => ({ ...prev, [fieldname]: e.target.value }));
  };

  const handleForm = async () => {
    if (!feedback) {
      setErrors({ message: "Заполните все необходимые поля!" });
    }

    if (!feedback?.name.trim()) {
      setErrors({ name: "Введите ваше имя!" });
    }

    if (
      !feedback?.email ||
      !feedback?.email.trim() ||
      !feedback?.phone_number ||
      !feedback?.phone_number.trim()
    ) {
      setErrors({ phone_number: "Заполните E-Mail или номер телефона!" });
    }

    if (!feedback?.message) {
      setErrors({ message: "Введите сообщение!" });
    }

    setErrors(null);
    setIsLoading(true);

    const res = await sendForm(feedback);

    if (res.status === "error") {
      setErrors({ message: res.message });
    }

    if (res.status === "success") {
      setFeedback({ name: "", email: "", phone_number: "", message: "" });
      addToast({ title: res.message, color: "success" });
    }

    setIsLoading(false);
  };

  return (
    <footer className="lg:px-[50px] px-5 pb-[48px]">
      <div className="grid md:grid-cols-[1.155fr_1fr] grid-cols-1 gap-4">
        <div className="md:px-[30px] md:py-5 p-5 bg-[#F3F5F8] dark:bg-[#2A2A2A] rounded-[30px] flex flex-col gap-[60px]">
          <div className="flex flex-col gap-5">
            <div className="flex items-center gap-[15px]">
              <Image
                className="w-[38px] h-[45px]"
                src={logo}
                alt="Logo NexLink"
              />
              <p className="text-[26px] leading-[100%] font-semibold">
                NexLink
              </p>
            </div>
            <Typography variant="p" className="!text-[16px]">
              {t("aboutCompany")}
            </Typography>
          </div>

          <div className="flex md:flex-row flex-col justify-between gap-5">
            <div className="flex flex-col gap-5">
              {PAGES().map((item) => (
                <Link
                  className="text-[22px] font-medium leading-[16px]"
                  key={item.name}
                  href={item.path}
                >
                  {item.name}
                </Link>
              ))}
            </div>
            <div className="flex flex-col md:justify-between gap-[24px]">
              <div className="flex justify-end gap-[17px]">
                <a href="#" target="_blank" rel="noopener noreferrer">
                  <VK />
                </a>
                <a href="#" target="_blank" rel="noopener noreferrer">
                  <Telegram />
                </a>
                <a href="#" target="_blank" rel="noopener noreferrer">
                  <Youtube />
                </a>
              </div>
              <span className="text-[18px] font-medium text-[#B344FF] md:text-end text-center">
                {t("copyright")}
              </span>
            </div>
          </div>
        </div>
        <div className="md:px-[30px] md:py-5 p-5 bg-[#F3F5F8] dark:bg-[#2A2A2A] rounded-[30px] flex flex-col gap-[37px] justify-between md:row-[1_/_2] md:col-[2_/_3] row-[1_/_2] col-[1_/_2]">
          <Typography variant="h5" className="!leading-[23px]">
            {t("feedback")}
          </Typography>

          <div className="flex flex-col gap-5">
            <Input
              value={feedback.name}
              error={errors?.name}
              onChange={(e) => handleFeedbackChanges(e, "name")}
              placeholder={t("name")}
              name="name"
            />
            <Input
              value={feedback.email}
              error={errors?.email}
              onChange={(e) => handleFeedbackChanges(e, "email")}
              placeholder="E-Mail"
              name="email"
            />
            <Input
              value={feedback.phone_number}
              error={errors?.phone_number}
              onChange={(e) => handleFeedbackChanges(e, "phone_number")}
              placeholder={t("phoneNumber")}
              name="phone_number"
            />
            <Input
              value={feedback.message}
              error={errors?.message}
              onChange={(e) => handleFeedbackChanges(e, "message")}
              placeholder={t("yourMessage")}
              name="message"
            />
            <Button onPress={handleForm} className="mt-[10px]">
              {isLoading ? <Spinner color="current" /> : t("send")}
            </Button>
          </div>
        </div>
        <Image
          src={nexlink}
          alt="NexLink.PRO"
          className="w-full md:col-[1_/_3]"
        />
      </div>
    </footer>
  );
};

export default Footer;
