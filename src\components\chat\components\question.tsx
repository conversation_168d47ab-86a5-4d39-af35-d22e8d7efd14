import { Button, ButtonProps } from "@heroui/button";

interface Props extends ButtonProps {
  children?: React.ReactNode;
}

const Question = ({ children, ...rest }: Props) => {
  return (
    <Button
      {...rest}
      className="p-[16px] bg-[#F3F5F8] dark:bg-[#2A2A2A] border-[1px] border-solid border-[#1818181A] rounded-[26px] text-[15px] font-medium leading-[130%] h-fit text-wrap whitespace-normal"
    >
      {children}
    </Button>
  );
};

export default Question;
