import React from 'react';
import { StatisticHighlight, StatisticText } from './StatisticParts';

export interface ParseRule {
  type: 'highlight' | 'text';
  pattern?: RegExp;
  words?: string[];
  className?: string;
}

export interface StatisticParserProps {
  text: string;
  rules: ParseRule[];
  defaultTextClassName?: string;
  defaultHighlightClassName?: string;
}

/**
 * Flexible parser that applies highlighting rules to text
 * Much more maintainable than complex regex in components
 */
export const StatisticParser: React.FC<StatisticParserProps> = ({
  text,
  rules,
  defaultTextClassName,
  defaultHighlightClassName
}) => {
  const parseText = (inputText: string): React.ReactNode[] => {
    const result: React.ReactNode[] = [];
    let remainingText = inputText;
    let keyCounter = 0;

    // Process each rule in order
    for (const rule of rules) {
      const newResult: React.ReactNode[] = [];
      
      for (const item of result.length > 0 ? result : [remainingText]) {
        if (typeof item !== 'string') {
          newResult.push(item);
          continue;
        }

        const textToProcess = item as string;
        let matches: { text: string; isMatch: boolean; index: number }[] = [];

        if (rule.type === 'highlight') {
          if (rule.pattern) {
            // Use regex pattern
            const regex = new RegExp(rule.pattern.source, rule.pattern.flags);
            const regexMatches = Array.from(textToProcess.matchAll(regex));
            
            matches = regexMatches.map(match => ({
              text: match[0],
              isMatch: true,
              index: match.index || 0
            }));
          } else if (rule.words) {
            // Use word list
            for (const word of rule.words) {
              const regex = new RegExp(`\\b${word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
              const wordMatches = Array.from(textToProcess.matchAll(regex));
              
              matches.push(...wordMatches.map(match => ({
                text: match[0],
                isMatch: true,
                index: match.index || 0
              })));
            }
          }
        }

        if (matches.length === 0) {
          newResult.push(textToProcess);
          continue;
        }

        // Sort matches by index
        matches.sort((a, b) => a.index - b.index);

        let lastIndex = 0;
        for (const match of matches) {
          // Add text before match
          if (match.index > lastIndex) {
            const beforeText = textToProcess.slice(lastIndex, match.index);
            if (beforeText) {
              newResult.push(beforeText);
            }
          }

          // Add highlighted match
          newResult.push(
            <StatisticHighlight 
              key={`highlight-${keyCounter++}`}
              className={rule.className || defaultHighlightClassName}
            >
              {match.text}
            </StatisticHighlight>
          );

          lastIndex = match.index + match.text.length;
        }

        // Add remaining text
        if (lastIndex < textToProcess.length) {
          const remainingPart = textToProcess.slice(lastIndex);
          if (remainingPart) {
            newResult.push(remainingPart);
          }
        }
      }

      result.length = 0;
      result.push(...newResult);
    }

    return result.length > 0 ? result : [text];
  };

  const parsedContent = parseText(text);

  return (
    <>
      {parsedContent.map((part, index) => 
        typeof part === 'string' ? (
          <StatisticText key={`text-${index}`} className={defaultTextClassName}>
            {part}
          </StatisticText>
        ) : (
          part
        )
      )}
    </>
  );
};
