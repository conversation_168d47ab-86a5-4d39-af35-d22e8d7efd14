import { apiFetch } from "@/shared/api-config/axios-config";
import { TeamProfile } from "../types/TeamProfile";
import { getLocale } from "next-intl/server";

export const getEmployees = async () => {
  const locale = await getLocale();
  console.log(locale);
  try {
    const res = await apiFetch<TeamProfile[]>(`employee/client`, {
      headers: {
        "accept-language": locale,
      },
    });
    return res;
  } catch (error) {
    console.error(error);

    return undefined;
  }
};
