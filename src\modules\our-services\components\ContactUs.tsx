"use client";

import OrderButton from "@/components/order-button/OrderButton";
import Typography from "@/components/typography";
import { setImageToText } from "@/shared/functions/setImageToText";
import { useTranslations } from "next-intl";
import ball from "@our-services/assets/shapes/ball.svg";
import FormModal from "@components/form-modal";
import { useDisclosure } from "@heroui/react";

const ContactUs = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const t = useTranslations("ourServices.contactsUs");
  console.log(isOpen);
  return (
    <div className="md:py-[260px] py-[95px] px-5 flex flex-col items-center md:gap-[40px] gap-5 max-w-[1150px] m-auto text-center">
      <div className="flex flex-col md:gap-[30px] gap-5">
        <Typography variant="h2">
          {setImageToText(t("title"), "Стоимость продукта", ball, {
            width: 135,
            height: 85,
          })}
        </Typography>
        <Typography variant="p">{t("description")}</Typography>
      </div>
      <OrderButton onPress={() => onOpen()}>{t("btnText")}</OrderButton>

      <FormModal handleClose={onClose} isOpen={isOpen} />
    </div>
  );
};

export default ContactUs;
