"use client";

import { useTheme } from "next-themes";
import { useCheckMount } from "../hooks/useCheckMount";

const Close = () => {
  const { theme } = useTheme();
  const isMounted = useCheckMount();

  if (!isMounted) return null;

  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18 6L6 18M18 18L6 6.00001"
        stroke={theme === "dark" ? "#ffffff" : "#181818"}
        strokeWidth="1.5"
        strokeLinecap="round"
      />
    </svg>
  );
};

export default Close;
