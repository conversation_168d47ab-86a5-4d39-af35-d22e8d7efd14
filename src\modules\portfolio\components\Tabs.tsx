"use client";

import <PERSON><PERSON> from "@/components/button";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface Props {
  items: { path: string; name: string }[];
}

const Tabs = ({ items }: Props) => {
  const pathname = usePathname();

  return (
    <div className="overflow-hidden overflow-x-auto max-w-full">
      <div className="flex gap-[6px] items-center tabs-container">
        {items.map((item) => (
          <Link key={item.path} href={item.path}>
            <Button
              className="font-medium sm:text-[18px] text-[16px]"
              buttonType={pathname === item.path ? "primary" : "default"}
            >
              {item.name}
            </Button>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default Tabs;
