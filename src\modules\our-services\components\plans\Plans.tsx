import Typography from "@/components/typography";
import { useTranslations } from "next-intl";
import Card from "./card";
import { PLANS } from "../../consts/plans";

const Plans = () => {
  const t = useTranslations("ourServices.plans");
  return (
    <div className="lg:px-[50px] p-5 flex flex-col gap-[60px] pb-[100px]">
      <div className="flex flex-col gap-5">
        <Typography variant="h4">{t("title")}</Typography>
        <Typography variant="p">{t("description")}</Typography>
      </div>
      <div className="grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1 gap-4">
        {PLANS().map((item) => (
          <Card
            key={item.title}
            title={item.title}
            description={item.description}
            includes={item.includes as string[]}
          />
        ))}
      </div>
    </div>
  );
};

export default Plans;
