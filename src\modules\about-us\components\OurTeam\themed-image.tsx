import { useTheme } from "next-themes";
import Image from "next/image";
import { useEffect, useState } from "react";

export function ThemedImage({
  light_image,
  dark_image,
}: {
  light_image: string;
  dark_image: string;
}) {
  const { resolvedTheme } = useTheme(); // 👈 использовать resolvedTheme
  const [currentImage, setCurrentImage] = useState<string | null>(null);

  useEffect(() => {
    if (resolvedTheme) {
      setCurrentImage(resolvedTheme === "light" ? light_image : dark_image);
    }
  }, [resolvedTheme, light_image, dark_image]);

  if (!currentImage) return null; // или placeholder, или skeleton

  return (
    <Image
      width={200}
      height={200}
      src={currentImage}
      alt="Image"
      className="!w-full !h-full object-cover object-top absolute top-0 left-0"
    />
  );
}
