import { useCheckMount } from "@/shared/hooks/useCheckMount";
import { useTheme } from "next-themes";

const ChevronDown = () => {
  const { theme } = useTheme();
  const isMounted = useCheckMount();

  if (!isMounted) return null;

  return (
    <svg
      width="10"
      height="6"
      viewBox="0 0 10 6"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 1L5.70707 4.29293C5.31818 4.68182 4.68182 4.68182 4.29293 4.29293L1 1"
        stroke={theme === "light" ? "#090909" : "#ffffff"}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default ChevronDown;
