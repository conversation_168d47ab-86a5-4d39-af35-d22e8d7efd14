import Typography from "@/components/typography";
import { useTranslations } from "next-intl";
import Card from "./card";
import img from "@home/assets/our-projects/img.svg";
import mainImg from "@home/assets/our-projects/mainImg.svg";
import Button from "@/components/button";

const OurProjects = () => {
  const t = useTranslations("home.ourProjects");
  return (
    <div className="lg:px-[50px] px-5 lg:py-[180px] py-[100px]">
      <div className="bg-[#F3F5F8] dark:bg-[#2A2A2A] lg:py-[70px] lg:px-[48px] p-5 rounded-[50px] flex flex-col lg:gap-[50px] gap-6">
        <Typography variant="h4" className="text-center max-w-[701px] m-auto">
          {t("title")}
        </Typography>

        <div className="flex flex-col lg:gap-[30px] gap-5">
          <div className="grid md:grid-cols-3 grid-cols-1 gap-4">
            <Card
              img={img}
              title={t("izde.title")}
              date={t("izde.date")}
              description={t("izde.description")}
              mainImage={mainImg}
              link="https://izde.online/"
            />

            <Card
              img={img}
              title={t("izde.title")}
              date={t("izde.date")}
              description={t("izde.description")}
              mainImage={mainImg}
              link="https://izde.online/"
            />

            <Card
              img={img}
              title={t("izde.title")}
              date={t("izde.date")}
              description={t("izde.description")}
              mainImage={mainImg}
              link="https://izde.online/"
            />
          </div>
          <Button>Подробнее</Button>
        </div>
      </div>
    </div>
  );
};

export default OurProjects;
