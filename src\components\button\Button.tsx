import { Button as <PERSON><PERSON>utt<PERSON> } from "@heroui/button";
import { ButtonProps } from "@heroui/react";

type ButtonType = "primary" | "default";

interface Props extends ButtonProps {
  buttonType?: ButtonType;
}

const Button = ({
  className,
  buttonType = "primary",
  isDisabled,
  ...rest
}: Props) => {
  const styles: Record<ButtonType, string> = {
    default: "bg-[#F3F5F8] dark:bg-[#2A2A2A] text-dark",
    primary:
      "bg-[linear-gradient(90deg,_#B344FF_0%,_rgba(179,68,255,0.7)_50%,_#D89CFF_100%)] text-white",
  };
  return (
    <HeroButton
      isDisabled={isDisabled}
      {...rest}
      className={`flex items-center justify-center gap-2 px-4 h-[50px] rounded-[42px] hover:brightness-95 transition-all font-bold text-[18px] leading-[100%] ${
        styles[buttonType]
      } ${className} ${isDisabled ? "opacity-70" : "opacity-100"}`}
    />
  );
};

export default Button;
