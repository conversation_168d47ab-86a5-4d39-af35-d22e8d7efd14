// import image1 from "@about-us/assets/team/team/team1.webp";
// import image2 from "@about-us/assets/team/team/team2.webp";
// import image3 from "@about-us/assets/team/team/team3.webp";
// import image4 from "@about-us/assets/team/team/team4.webp";
// import image5 from "@about-us/assets/team/team/team5.webp";
// import image6 from "@about-us/assets/team/team/team6.webp";
// import image7 from "@about-us/assets/team/team/team7.webp";
// import image8 from "@about-us/assets/team/team/team8.webp";
// import image9 from "@about-us/assets/team/team/team9.webp";
// import image10 from "@about-us/assets/team/team/team10.webp";
// import image11 from "@about-us/assets/team/team/team11.webp";
// import image12 from "@about-us/assets/team/team/team12.webp";
// import image13 from "@about-us/assets/team/team/team13.webp";
// import image14 from "@about-us/assets/team/team/team14.webp";
// import image15 from "@about-us/assets/team/team/team15.webp";
// import image16 from "@about-us/assets/team/team/team16.webp";
// import image17 from "@about-us/assets/team/team/team17.webp";
// import image18 from "@about-us/assets/team/team/team18.webp";

// import aida_dark from "@about-us/assets/team/dark/aida_dark.webp";
// import aida_light from "@about-us/assets/team/light/aida_light.webp";

// import kalys_dark from "@about-us/assets/team/dark/kalys_dark.webp";
// import kalys_light from "@about-us/assets/team/light/kalys_light.webp";

// import syimyk_dark from "@about-us/assets/team/dark/syimyk_dark.webp";
// import syimyk_light from "@about-us/assets/team/light/syimyk_light.webp";

// import bekjan_dark from "@about-us/assets/team/dark/bekjan_dark.webp";
// import bekjan_light from "@about-us/assets/team/light/bekjan_light.webp";

// import talgat_dark from "@about-us/assets/team/dark/talgat_dark.webp";
// import talgat_light from "@about-us/assets/team/light/talgat_light.webp";

// import bekbolsun_dark from "@about-us/assets/team/dark/bekbolsun_dark.webp";
// import bekbolsun_light from "@about-us/assets/team/light/bekbolsun_light.webp";

// import nursultan_dark from "@about-us/assets/team/dark/nursultan_dark.webp";
// import nursultan_light from "@about-us/assets/team/light/nursultan_light.webp";

// import islam_dark from "@about-us/assets/team/dark/islam_dark.webp";
// import islam_light from "@about-us/assets/team/light/islam_light.webp";

// import begayim_dark from "@about-us/assets/team/dark/begayim_dark.webp";
// import begayim_light from "@about-us/assets/team/light/begayim_light.webp";

// import aziza_dark from "@about-us/assets/team/dark/aziza_dark.webp";
// import aziza_light from "@about-us/assets/team/light/aziza_light.webp";

// import eldiar_dark from "@about-us/assets/team/dark/eldiar_dark.webp";
// import eldiar_light from "@about-us/assets/team/light/eldiar_light.webp";

// import bayel_dark from "@about-us/assets/team/dark/bayel_dark.webp";
// import bayel_light from "@about-us/assets/team/light/bayel_light.webp";

// import ali_dark from "@about-us/assets/team/dark/ali_dark.webp";
// import ali_light from "@about-us/assets/team/light/ali_light.webp";

// import kadyr_dark from "@about-us/assets/team/dark/kadyr_dark.webp";
// import kadyr_light from "@about-us/assets/team/light/kadyr_light.webp";

// import ernazar_dark from "@about-us/assets/team/dark/ernazar_dark.webp";
// import ernazar_light from "@about-us/assets/team/light/ernazar_light.webp";

// export const TEAM_PICTURES = [
//   image1,
//   image2,
//   image3,
//   image4,
//   image5,
//   image6,
//   image7,
//   image8,
//   image9,
//   image10,
//   image11,
//   image12,
//   image13,
//   image14,
//   image15,
//   image16,
//   image17,
//   image18,
// ];

// export const TEMA_PROFILES = () => {
//   return [
//     {
//       dir: "ceo",
//       team: [
//         {
//           name: "Аида",
//           position: "Руководитель и Основатель компании",
//           stack: "Jira, Confluence, Trello, ClickUp",
//           darkImage: aida_dark,
//           lightImage: aida_light,
//         },
//       ],
//     },

//     {
//       dir: "Q A",
//       team: [
//         {
//           name: "Калыс",
//           position: "Q A, Project Manager",
//           stack:
//             "Java, Selenium WebDriver, Git, TestNG, JUnit, Maven, Allure, Jenkins, TestRail, Jira, ",
//           darkImage: kalys_dark,
//           lightImage: kalys_light,
//         },
//       ],
//     },

//     {
//       dir: "TEAM LEADS",
//       team: [
//         {
//           name: "Калыс",
//           position: "Q A, Project Manager",
//           stack:
//             "Java, Selenium WebDriver, Git, TestNG, JUnit, Maven, Allure, Jenkins, TestRail, Jira, ",
//           darkImage: kalys_dark,
//           lightImage: kalys_light,
//         },

//         {
//           name: "Сыймык",
//           position: "Lead UX/UI Designer",
//           stack: "Figma, Scratch, Illustrator, Fotoshop, Web-searching",
//           darkImage: syimyk_dark,
//           lightImage: syimyk_light,
//         },

//         {
//           name: "Бекжан",
//           position: "Lead Android Developer",
//           stack:
//             "kotlin, clean architecture, MVI, bloc, get_it, dagger hilt, dio, retrofit, web socket, room, drift, firebase, OAuth2, play console.",
//           darkImage: bekjan_dark,
//           lightImage: bekjan_light,
//         },

//         {
//           name: "Талгат",
//           position: "Lead Front-End Developer",
//           stack: "Git, react, js, ts, next.js, html, css, zustand.",
//           darkImage: talgat_dark,
//           lightImage: talgat_light,
//         },

//         {
//           name: "Бекболсун",
//           position: "Lead Back-End Developer",
//           stack:
//             "Python, Django, FastAPI, PostgreSQL, Git, Celery, Redis, DRF, REST API, Network, Aws, ETL, RabbitMq, Web Socket, Martin, GeoAlchemy, Map Vector Tiles, GeoJson, Qgis, Esri, DevOps(Terraform, Ansible, Docker, BuildX, CI/CD, Backup, DNS, Network), Asyncio, Uvloop, Pypy, ",
//           darkImage: bekbolsun_dark,
//           lightImage: bekbolsun_light,
//         },

//         {
//           name: "Нурсултан",
//           position: "Lead Flutter Developer",
//           stack: "Flutter, Dart, Clean Architecture, MVVM.",
//           darkImage: nursultan_dark,
//           lightImage: nursultan_light,
//         },

//         {
//           name: "Ислам",
//           position: "Lead IOS Developer",
//           stack:
//             "Swift, UiKit, ARC, Client-server applications, Networking, Push, Rest api, FireBase, GCD, Figma, Jira, Git, Swift Package Manager, Cocoapods, RxSwift, RxCocoa, MVP, MVC, MVVM, Moya, Alamofire, UrlSession, SnapKit, AutoLayout",
//           darkImage: islam_dark,
//           lightImage: islam_light,
//         },
//       ],
//     },

//     {
//       dir: "UX/UI, WEB Дизайн",
//       team: [
//         {
//           name: "Сыймык",
//           position: "Lead UX/UI Designer",
//           stack: "Figma, Scratch, Illustrator, Fotoshop, Web-searching",
//           darkImage: syimyk_dark,
//           lightImage: syimyk_light,
//         },

//         {
//           name: "Бегайым",
//           position: "UX/UI Designer",
//           stack: "",
//           darkImage: begayim_dark,
//           lightImage: begayim_light,
//         },
//       ],
//     },

//     {
//       dir: "BACK-END",
//       team: [
//         {
//           name: "Бекболсун",
//           position: "Lead Back-End Developer",
//           stack:
//             "Python, Django, FastAPI, PostgreSQL, Git, Celery, Redis, DRF, REST API, Network, Aws, ETL, RabbitMq, Web Socket, Martin, GeoAlchemy, Map Vector Tiles, GeoJson, Qgis, Esri, DevOps(Terraform, Ansible, Docker, BuildX, CI/CD, Backup, DNS, Network), Asyncio, Uvloop, Pypy, ",
//           darkImage: bekbolsun_dark,
//           lightImage: bekbolsun_light,
//         },

//         {
//           name: "Азиза",
//           position: "Back-End Developer",
//           stack:
//             "Python, Django, FastAPI, PostgreSQL, Git, Celery, Redis, DRF, SQL, Docker, HTML, REST API.",
//           darkImage: eldiar_dark,
//           lightImage: eldiar_light,
//         },

//         {
//           name: "Элдияр",
//           position: "Back-End Developer",
//           stack:
//             "Python 3, OOP, Django, FastAPI, SQLAlchemy, Asyncio, Aiogram 2.5-3, REST API, WebSockets, gRPC, Unit Tests, Pytest, Redis, Celery, RabbitMQ, JS, AJAX, Stripe, SQL (SQLite, PostgreSQL, MySQL), Git (GitHub, GitLab, Microsoft Azure), Linux, Nginx, Docker, CI/CD, AWS(S3, SES, SNS, CloudWatch, EC2), Cloudflare",
//           darkImage: aziza_dark,
//           lightImage: aziza_light,
//         },

//         {
//           name: "Алибек",
//           position: "Full Stack Developer",
//           stack:
//             "React, Nextjs, Nodejs, Nestjs, PostgreSQL, Docker, CI/CD, AWS, Git",
//           darkImage: ali_dark,
//           lightImage: ali_light,
//         },
//       ],
//     },

//     {
//       dir: "FRONT-END",
//       team: [
//         {
//           name: "Талгат",
//           position: "Lead Front-End Developer",
//           stack: "Git, react, js, ts, next.js, html, css, zustand",
//           darkImage: talgat_dark,
//           lightImage: talgat_light,
//         },

//         {
//           name: "Байэл",
//           position: "Front-End Developer",
//           stack: "React, Next.js, TS, Scss, redux/zustand, CI/CD, Docker , Git",
//           darkImage: bayel_dark,
//           lightImage: bayel_light,
//         },

//         {
//           name: "Алибек",
//           position: "Full Stack Developer",
//           stack:
//             "React, Nextjs, Nodejs, Nestjs, PostgreSQL, Docker, CI/CD, AWS, Git",
//           darkImage: ali_dark,
//           lightImage: ali_light,
//         },
//       ],
//     },

//     {
//       dir: "Mobile Developers",
//       team: [
//         {
//           name: "Бекжан",
//           position: "Lead Android Developer",
//           stack:
//             "kotlin, clean architecture, MVI, bloc, get_it, dagger hilt, dio, retrofit, web socket, room, drift, firebase, OAuth2, play console.",
//           darkImage: bekjan_dark,
//           lightImage: bekjan_light,
//         },

//         {
//           name: "Нурсултан",
//           position: "Lead Flutter Developer",
//           stack: "Flutter, Dart, Clean Architecture, MVVM.",
//           darkImage: nursultan_dark,
//           lightImage: nursultan_light,
//         },

//         {
//           name: "Ислам",
//           position: "Lead IOS Developer",
//           stack:
//             "Swift, UiKit, ARC, Client-server applications, Networking, Push, Rest api, FireBase, GCD, Figma, Jira, Git, Swift Package Manager, Cocoapods, RxSwift, RxCocoa, MVP, MVC, MVVM, Moya, Alamofire, UrlSession, SnapKit, AutoLayout",
//           darkImage: islam_dark,
//           lightImage: islam_light,
//         },

//         {
//           name: "Эрназар",
//           position: "Flutter Developer",
//           stack:
//             "Dart, Flutter, Swift, UIKit, SwiftUI,, RxSwift, GCD, BLoC, Provider, MVVM, GitHub/GitLab, Git, Apple connect.",
//           darkImage: ernazar_dark,
//           lightImage: ernazar_light,
//         },

//         {
//           name: "Кадыр",
//           position: "Flutter Developer",
//           stack: "",
//           darkImage: kadyr_dark,
//           lightImage: kadyr_light,
//         },
//       ],
//     },
//   ];
// };
