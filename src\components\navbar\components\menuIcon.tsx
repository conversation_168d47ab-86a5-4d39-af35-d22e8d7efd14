import { useCheckMount } from "@/shared/hooks/useCheckMount";
import { useTheme } from "next-themes";

const MenuIcon = () => {
  const { theme } = useTheme();
  const isMounted = useCheckMount();

  if (!isMounted) return null;

  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4 5H20M12 12H20M4 19H20"
        stroke={theme === "light" ? "black" : "#ffffff"}
        strokeWidth="2"
        strokeLinecap="round"
      />
    </svg>
  );
};

export default MenuIcon;
