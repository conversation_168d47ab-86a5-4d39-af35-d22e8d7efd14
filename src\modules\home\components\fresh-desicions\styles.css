.sphere::before {
  content: "";
  display: inline-block;
  background-image: url(../../assets/fresh-desicions/sphere.svg);
  background-size: contain;
  background-repeat: no-repeat;
  width: 81px;
  height: 81px;
  vertical-align: middle;
}

.cube::before {
  content: "";
  display: inline-block;
  background-image: url(../../assets/fresh-desicions/cube.svg);
  background-size: contain;
  background-repeat: no-repeat;
  width: 76px;
  height: 76px;
  vertical-align: middle;
}

@media screen and (max-width: 1024px) {
  .sphere::before {
    width: 70px;
    height: 70px;
  }
}

@media screen and (max-width: 768px) {
  .sphere::before {
    width: 32px;
    height: 32px;
  }

  .cube::before {
    width: 32px;
    height: 32px;
  }
}
