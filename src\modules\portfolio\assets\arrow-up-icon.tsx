"use client";

import { useCheckMount } from "@/shared/hooks/useCheckMount";
import { useTheme } from "next-themes";

const ArrowUpIcon = () => {
  const { theme } = useTheme();
  const isMounted = useCheckMount();

  if (!isMounted) return null;
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.4573 10.662L8 22.118L9.88204 24L21.338 12.5454V22.641H24V8H9.35896V10.662H19.4573Z"
        fill={theme === "light" ? "#181818" : "white"}
      />
    </svg>
  );
};

export default ArrowUpIcon;
