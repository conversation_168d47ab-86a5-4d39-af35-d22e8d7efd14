{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": "src", "paths": {"@/*": ["*"], "@shared/*": ["shared/*"], "@components/*": ["components/*"], "@navigation/*": ["i18n/*"], "@home/*": ["modules/home/<USER>"], "@our-services/*": ["modules/our-services/*"], "@portfolio/*": ["modules/portfolio/*"], "@contacts/*": ["modules/contacts/*"], "@about-us/*": ["modules/about-us/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}