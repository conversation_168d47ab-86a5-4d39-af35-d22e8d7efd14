"use client";

import Tag from "@/components/tag";
import Typography from "@/components/typography";
import { useTranslations } from "next-intl";
import { STATISTICS } from "../../consts/statistics";
import Card from "./card";

const AboutCompany = () => {
  const t = useTranslations("home.aboutCompany");
  return (
    <div className="flex flex-col md:gap-10 gap-[30px] lg:px-[50px] px-5">
      <div className="flex flex-col md:gap-[16px] gap-[10px] text-center max-w-[1020px] m-auto">
        <div className="flex flex-col items-center md:gap-2.5 gap-4">
          <Tag>{t("preTitle")}</Tag>
          <Typography className="sm:break-normal" variant="h4">
            {t("title")}
          </Typography>
        </div>
        <Typography variant="p">{t("description")}</Typography>
      </div>

      <div className="grid lg:grid-cols-4 sm:grid-cols-2 grid-cols-1 md:gap-4 gap-[11px]">
        {STATISTICS().map((item) => (
          <Card
            isLast={item.isLast}
            key={item.title}
            title={item.title}
            description={item.description}
            img={item.img}
          />
        ))}
      </div>
    </div>
  );
};

export default AboutCompany;
