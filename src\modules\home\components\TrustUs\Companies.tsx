"use client";

import { useTheme } from "next-themes";
import { useEffect, useMemo, useState } from "react";
import Image, { StaticImageData } from "next/image";

import airalo from "@shared/assets/companies/airalo.svg";
import infoservice from "@shared/assets/companies/infoservice.svg";
import maxi from "@shared/assets/companies/maxi.svg";
import eletcom from "@shared/assets/companies/eletcom.svg";
import emiprint from "@shared/assets/companies/emiprint.svg";
import etrust from "@shared/assets/companies/etrust.svg";

import airalo_light from "@shared/assets/companies/airalo_light.svg";
import infoservice_light from "@shared/assets/companies/infoservice_light.svg";
import maxi_light from "@shared/assets/companies/maxi_light.svg";
import eletcom_light from "@shared/assets/companies/eletcom_light.svg";
import emiprint_light from "@shared/assets/companies/emiprint_light.svg";
import etrust_light from "@shared/assets/companies/etrust_light.svg";

type Props = {
  serverTheme: "light" | "dark";
  isRow?: boolean;
};

const Companies = ({ serverTheme, isRow }: Props) => {
  const { theme: clientTheme } = useTheme();
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const effectiveTheme = isMounted ? clientTheme ?? serverTheme : serverTheme;

  const logos: StaticImageData[] = useMemo(() => {
    return effectiveTheme === "light"
      ? [airalo, infoservice, maxi, eletcom, emiprint, etrust]
      : [
          airalo_light,
          infoservice_light,
          maxi_light,
          eletcom_light,
          emiprint_light,
          etrust_light,
        ];
  }, [effectiveTheme]);

  return (
    <div
      className={`flex items-center md:gap-y-[20px] gap-y-[16px] opacity-60 ${
        isRow
          ? "flex-nowrap overflow-hidden overflow-x-auto md:gap-x-[30px] gap-x-[20px]"
          : "flex-wrap justify-center md:gap-x-[60px] sm:gap-x-[30px] gap-x-[20px]"
      }`}
    >
      {logos.map((item, i) => (
        <Image key={i} src={item} alt="Company logo" />
      ))}
    </div>
  );
};

export default Companies;
