import { useCheckMount } from "@/shared/hooks/useCheckMount";
import { useTheme } from "next-themes";

const GooglePlay = () => {
  const { theme } = useTheme();
  const isMounted = useCheckMount();

  if (!isMounted) return;
  return theme === "light" ? (
    <svg
      width="230"
      height="60"
      viewBox="0 0 230 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="230" height="60" rx="11" fill="#181818" />
      <path
        d="M20.6175 13.7975C20.2264 14.1973 20 14.8196 20 15.6256V44.3751C20 45.1811 20.2264 45.8035 20.6175 46.2033L20.714 46.291L37.2146 30.1873V29.807L20.714 13.7033L20.6175 13.7975Z"
        fill="url(#paint0_linear_1007_4093)"
      />
      <path
        d="M42.7089 35.5586L37.2148 30.188V29.8077L42.7156 24.4371L42.8388 24.507L49.3531 28.1258C51.2122 29.1528 51.2122 30.8428 49.3531 31.8763L42.8388 35.4887L42.7089 35.5586Z"
        fill="url(#paint1_linear_1007_4093)"
      />
      <path
        d="M42.8383 35.4883L37.2143 29.9974L20.6172 46.2035C21.2347 46.8373 22.2416 46.9137 23.3867 46.2799L42.8383 35.4883Z"
        fill="url(#paint2_linear_1007_4093)"
      />
      <path
        d="M42.8383 24.5072L23.3867 13.7156C22.2416 13.0883 21.2347 13.1647 20.6172 13.7984L37.2143 29.998L42.8383 24.5072Z"
        fill="url(#paint3_linear_1007_4093)"
      />
      <path
        d="M70.8226 16.2459C70.8226 17.3314 70.4897 18.2008 69.834 18.8491C69.08 19.6178 68.098 20.0045 66.8947 20.0045C65.7446 20.0045 64.7626 19.6113 63.9571 18.8361C63.1498 18.0496 62.7471 17.0844 62.7471 15.929C62.7471 14.7736 63.1498 13.8084 63.9571 13.0284C64.7626 12.2468 65.7446 11.8535 66.8947 11.8535C67.4672 11.8535 68.0132 11.9689 68.5341 12.1834C69.0534 12.3995 69.4761 12.692 69.7824 13.0528L69.0867 13.7385C68.5524 13.1226 67.8251 12.8188 66.8947 12.8188C66.0559 12.8188 65.3285 13.1048 64.711 13.6816C64.1002 14.2601 63.794 15.0093 63.794 15.929C63.794 16.8488 64.1002 17.6044 64.711 18.1829C65.3285 18.7533 66.0559 19.0458 66.8947 19.0458C67.7851 19.0458 68.5341 18.7533 69.125 18.1764C69.5144 17.7945 69.7358 17.268 69.794 16.5953H66.8947V15.656H70.7627C70.8093 15.8591 70.8226 16.0558 70.8226 16.2459Z"
        fill="white"
        stroke="white"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M76.9559 12.9903H73.3225V15.4603H76.598V16.3996H73.3225V18.8696H76.9559V19.8267H72.2939V12.0332H76.9559V12.9903Z"
        fill="white"
        stroke="white"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M81.2894 19.8267H80.2608V12.9903H78.0322V12.0332H83.5197V12.9903H81.2894V19.8267Z"
        fill="white"
        stroke="white"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M87.4893 19.8267V12.0332H88.5162V19.8267H87.4893Z"
        fill="white"
        stroke="white"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M93.0667 19.8267H92.0465V12.9903H89.8096V12.0332H95.3037V12.9903H93.0667V19.8267Z"
        fill="white"
        stroke="white"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M105.696 18.8231C104.909 19.6113 103.934 20.0045 102.77 20.0045C101.6 20.0045 100.625 19.6113 99.8378 18.8231C99.0523 18.0366 98.6611 17.0714 98.6611 15.929C98.6611 14.7866 99.0523 13.8214 99.8378 13.0349C100.625 12.2468 101.6 11.8535 102.77 11.8535C103.927 11.8535 104.903 12.2468 105.69 13.0414C106.482 13.8344 106.873 14.7931 106.873 15.929C106.873 17.0714 106.482 18.0366 105.696 18.8231ZM100.598 18.1699C101.191 18.7533 101.912 19.0458 102.77 19.0458C103.623 19.0458 104.35 18.7533 104.936 18.1699C105.527 17.5865 105.826 16.8374 105.826 15.929C105.826 15.0206 105.527 14.2715 104.936 13.6881C104.35 13.1048 103.623 12.8123 102.77 12.8123C101.912 12.8123 101.191 13.1048 100.598 13.6881C100.008 14.2715 99.708 15.0206 99.708 15.929C99.708 16.8374 100.008 17.5865 100.598 18.1699Z"
        fill="white"
        stroke="white"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M108.313 19.8267V12.0332H109.562L113.443 18.0945H113.488L113.443 16.5962V12.0332H114.47V19.8267H113.398L109.334 13.4665H109.289L109.334 14.9712V19.8267H108.313Z"
        fill="white"
        stroke="white"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M98.8241 32.122C95.6967 32.122 93.1419 34.4458 93.1419 37.6519C93.1419 40.832 95.6967 43.1802 98.8241 43.1802C101.958 43.1802 104.513 40.832 104.513 37.6519C104.513 34.4458 101.958 32.122 98.8241 32.122ZM98.8241 41.0027C97.1081 41.0027 95.6318 39.6198 95.6318 37.6519C95.6318 35.658 97.1081 34.2995 98.8241 34.2995C100.54 34.2995 102.023 35.658 102.023 37.6519C102.023 39.6198 100.54 41.0027 98.8241 41.0027ZM86.4261 32.122C83.2921 32.122 80.744 34.4458 80.744 37.6519C80.744 40.832 83.2921 43.1802 86.4261 43.1802C89.5585 43.1802 92.1083 40.832 92.1083 37.6519C92.1083 34.4458 89.5585 32.122 86.4261 32.122ZM86.4261 41.0027C84.7085 41.0027 83.2272 39.6198 83.2272 37.6519C83.2272 35.658 84.7085 34.2995 86.4261 34.2995C88.1421 34.2995 89.6184 35.658 89.6184 37.6519C89.6184 39.6198 88.1421 41.0027 86.4261 41.0027ZM71.6731 33.8169V36.1667H77.4202C77.2521 37.4797 76.8027 38.4449 76.1137 39.1177C75.2748 39.9302 73.9683 40.832 71.6731 40.832C68.1363 40.832 65.3668 38.0452 65.3668 34.592C65.3668 31.1389 68.1363 28.352 71.6731 28.352C73.5855 28.352 74.9769 29.0817 76.0038 30.0274L77.6998 28.3715C76.2635 27.0325 74.3528 26.0039 71.6731 26.0039C66.8231 26.0039 62.7471 29.8568 62.7471 34.592C62.7471 39.3273 66.8231 43.1802 71.6731 43.1802C74.2945 43.1802 76.2635 42.3417 77.8113 40.7687C79.3975 39.22 79.8918 37.0425 79.8918 35.2843C79.8918 34.7383 79.8452 34.2362 79.762 33.8169H71.6731ZM132 35.6385C131.532 34.4019 130.089 32.122 127.15 32.122C124.237 32.122 121.812 34.3629 121.812 37.6519C121.812 40.7492 124.212 43.1802 127.43 43.1802C130.031 43.1802 131.532 41.6315 132.15 40.7297L130.219 39.4735C129.575 40.3933 128.698 41.0027 127.43 41.0027C126.17 41.0027 125.266 40.4388 124.687 39.3273L132.261 36.2674L132 35.6385ZM124.277 37.4797C124.212 35.3477 125.973 34.2557 127.235 34.2557C128.224 34.2557 129.062 34.7383 129.342 35.4289L124.277 37.4797ZM118.121 42.8438H120.611V26.5938H118.121V42.8438ZM114.043 33.3538H113.96C113.401 32.707 112.334 32.122 110.982 32.122C108.146 32.122 105.553 34.553 105.553 37.6698C105.553 40.7687 108.146 43.1802 110.982 43.1802C112.334 43.1802 113.401 42.5903 113.96 41.924H114.043V42.717C114.043 44.8312 112.886 45.967 111.021 45.967C109.499 45.967 108.556 44.8945 108.166 43.9927L106.001 44.875C106.625 46.3408 108.278 48.1445 111.021 48.1445C113.94 48.1445 116.403 46.4675 116.403 42.3872V32.4584H114.043V33.3538ZM111.195 41.0027C109.479 41.0027 108.043 39.6003 108.043 37.6698C108.043 35.7214 109.479 34.2995 111.195 34.2995C112.886 34.2995 114.22 35.7214 114.22 37.6698C114.22 39.6003 112.886 41.0027 111.195 41.0027ZM143.657 26.5938H137.702V42.8438H140.185V36.6867H143.657C146.415 36.6867 149.12 34.7383 149.12 31.6394C149.12 28.5422 146.409 26.5938 143.657 26.5938ZM143.722 34.4263H140.185V28.8542H143.722C145.576 28.8542 146.635 30.3573 146.635 31.6394C146.635 32.8972 145.576 34.4263 143.722 34.4263ZM159.073 32.0912C157.279 32.0912 155.413 32.8647 154.646 34.579L156.849 35.4809C157.324 34.579 158.196 34.2865 159.118 34.2865C160.406 34.2865 161.712 35.0422 161.732 36.3763V36.5469C161.283 36.2934 160.321 35.918 159.138 35.918C156.764 35.918 154.346 37.1937 154.346 39.5743C154.346 41.7518 156.29 43.1542 158.475 43.1542C160.146 43.1542 161.068 42.418 161.648 41.5617H161.732V42.8178H164.131V36.5843C164.131 33.7032 161.927 32.0912 159.073 32.0912ZM158.773 40.9962C157.961 40.9962 156.829 40.6029 156.829 39.6198C156.829 38.362 158.241 37.8794 159.462 37.8794C160.556 37.8794 161.068 38.115 161.732 38.4254C161.538 39.9302 160.211 40.9962 158.773 40.9962ZM172.869 32.447L170.014 39.4914H169.93L166.979 32.447H164.301L168.734 42.2913L166.205 47.7692H168.799L175.632 32.447H172.869ZM150.485 42.8438H152.975V26.5938H150.485V42.8438Z"
        fill="white"
      />
      <path
        d="M189.636 29.7148L208.636 29.7148M203.636 35.7148L208.929 30.422C209.319 30.0314 209.319 29.3983 208.929 29.0077L203.636 23.7148"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <defs>
        <linearGradient
          id="paint0_linear_1007_4093"
          x1="35.7496"
          y1="44.6745"
          x2="13.9387"
          y2="22.3352"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#00A0FF" />
          <stop offset="0.0066" stopColor="#00A1FF" />
          <stop offset="0.2601" stopColor="#00BEFF" />
          <stop offset="0.5122" stopColor="#00D2FF" />
          <stop offset="0.7604" stopColor="#00DFFF" />
          <stop offset="1" stopColor="#00E3FF" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_1007_4093"
          x1="51.774"
          y1="29.996"
          x2="19.5558"
          y2="29.996"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFE000" />
          <stop offset="0.4087" stopColor="#FFBD00" />
          <stop offset="0.7754" stopColor="#FFA500" />
          <stop offset="1" stopColor="#FF9C00" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_1007_4093"
          x1="39.7801"
          y1="27.0125"
          x2="10.2028"
          y2="-3.28153"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FF3A44" />
          <stop offset="1" stopColor="#C31162" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_1007_4093"
          x1="16.4393"
          y1="55.769"
          x2="29.6469"
          y2="42.2415"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#32A071" />
          <stop offset="0.0685" stopColor="#2DA771" />
          <stop offset="0.4762" stopColor="#15CF74" />
          <stop offset="0.8009" stopColor="#06E775" />
          <stop offset="1" stopColor="#00F076" />
        </linearGradient>
      </defs>
    </svg>
  ) : (
    <svg
      width="230"
      height="60"
      viewBox="0 0 230 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="230" height="60" rx="11" fill="white" />
      <path
        d="M20.6175 13.7975C20.2264 14.1973 20 14.8196 20 15.6256V44.3751C20 45.1811 20.2264 45.8035 20.6175 46.2033L20.714 46.291L37.2146 30.1873V29.807L20.714 13.7033L20.6175 13.7975Z"
        fill="url(#paint0_linear_1359_1819)"
      />
      <path
        d="M42.7089 35.5586L37.2148 30.188V29.8077L42.7156 24.4371L42.8388 24.507L49.3531 28.1258C51.2122 29.1528 51.2122 30.8428 49.3531 31.8763L42.8388 35.4887L42.7089 35.5586Z"
        fill="url(#paint1_linear_1359_1819)"
      />
      <path
        d="M42.8383 35.4883L37.2143 29.9974L20.6172 46.2035C21.2347 46.8373 22.2416 46.9137 23.3867 46.2799L42.8383 35.4883Z"
        fill="url(#paint2_linear_1359_1819)"
      />
      <path
        d="M42.8383 24.5072L23.3867 13.7156C22.2416 13.0883 21.2347 13.1647 20.6172 13.7984L37.2143 29.998L42.8383 24.5072Z"
        fill="url(#paint3_linear_1359_1819)"
      />
      <path
        d="M70.8226 16.2459C70.8226 17.3314 70.4897 18.2008 69.834 18.8491C69.08 19.6178 68.098 20.0045 66.8947 20.0045C65.7446 20.0045 64.7626 19.6113 63.9571 18.8361C63.1498 18.0496 62.7471 17.0844 62.7471 15.929C62.7471 14.7736 63.1498 13.8084 63.9571 13.0284C64.7626 12.2468 65.7446 11.8535 66.8947 11.8535C67.4672 11.8535 68.0132 11.9689 68.5341 12.1834C69.0534 12.3995 69.4761 12.692 69.7824 13.0528L69.0867 13.7385C68.5524 13.1226 67.8251 12.8188 66.8947 12.8188C66.0559 12.8188 65.3285 13.1048 64.711 13.6816C64.1002 14.2601 63.794 15.0093 63.794 15.929C63.794 16.8488 64.1002 17.6044 64.711 18.1829C65.3285 18.7533 66.0559 19.0458 66.8947 19.0458C67.7851 19.0458 68.5341 18.7533 69.125 18.1764C69.5144 17.7945 69.7358 17.268 69.794 16.5953H66.8947V15.656H70.7627C70.8093 15.8591 70.8226 16.0558 70.8226 16.2459Z"
        fill="#181818"
        stroke="#181818"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M76.9559 12.9903H73.3225V15.4603H76.598V16.3996H73.3225V18.8696H76.9559V19.8267H72.2939V12.0332H76.9559V12.9903Z"
        fill="#181818"
        stroke="#181818"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M81.2894 19.8267H80.2608V12.9903H78.0322V12.0332H83.5197V12.9903H81.2894V19.8267Z"
        fill="#181818"
        stroke="#181818"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M87.4893 19.8267V12.0332H88.5162V19.8267H87.4893Z"
        fill="#181818"
        stroke="#181818"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M93.0667 19.8267H92.0465V12.9903H89.8096V12.0332H95.3037V12.9903H93.0667V19.8267Z"
        fill="#181818"
        stroke="#181818"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M105.696 18.8231C104.909 19.6113 103.934 20.0045 102.77 20.0045C101.6 20.0045 100.625 19.6113 99.8378 18.8231C99.0523 18.0366 98.6611 17.0714 98.6611 15.929C98.6611 14.7866 99.0523 13.8214 99.8378 13.0349C100.625 12.2468 101.6 11.8535 102.77 11.8535C103.927 11.8535 104.903 12.2468 105.69 13.0414C106.482 13.8344 106.873 14.7931 106.873 15.929C106.873 17.0714 106.482 18.0366 105.696 18.8231ZM100.598 18.1699C101.191 18.7533 101.912 19.0458 102.77 19.0458C103.623 19.0458 104.35 18.7533 104.936 18.1699C105.527 17.5865 105.826 16.8374 105.826 15.929C105.826 15.0206 105.527 14.2715 104.936 13.6881C104.35 13.1048 103.623 12.8123 102.77 12.8123C101.912 12.8123 101.191 13.1048 100.598 13.6881C100.008 14.2715 99.708 15.0206 99.708 15.929C99.708 16.8374 100.008 17.5865 100.598 18.1699Z"
        fill="#181818"
        stroke="#181818"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M108.313 19.8267V12.0332H109.562L113.443 18.0945H113.488L113.443 16.5962V12.0332H114.47V19.8267H113.398L109.334 13.4665H109.289L109.334 14.9712V19.8267H108.313Z"
        fill="#181818"
        stroke="#181818"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M98.8241 32.122C95.6967 32.122 93.1419 34.4458 93.1419 37.6519C93.1419 40.832 95.6967 43.1802 98.8241 43.1802C101.958 43.1802 104.513 40.832 104.513 37.6519C104.513 34.4458 101.958 32.122 98.8241 32.122ZM98.8241 41.0027C97.1081 41.0027 95.6318 39.6198 95.6318 37.6519C95.6318 35.658 97.1081 34.2995 98.8241 34.2995C100.54 34.2995 102.023 35.658 102.023 37.6519C102.023 39.6198 100.54 41.0027 98.8241 41.0027ZM86.4261 32.122C83.2921 32.122 80.744 34.4458 80.744 37.6519C80.744 40.832 83.2921 43.1802 86.4261 43.1802C89.5585 43.1802 92.1083 40.832 92.1083 37.6519C92.1083 34.4458 89.5585 32.122 86.4261 32.122ZM86.4261 41.0027C84.7085 41.0027 83.2272 39.6198 83.2272 37.6519C83.2272 35.658 84.7085 34.2995 86.4261 34.2995C88.1421 34.2995 89.6184 35.658 89.6184 37.6519C89.6184 39.6198 88.1421 41.0027 86.4261 41.0027ZM71.6731 33.8169V36.1667H77.4202C77.2521 37.4797 76.8027 38.4449 76.1137 39.1177C75.2748 39.9302 73.9683 40.832 71.6731 40.832C68.1363 40.832 65.3668 38.0452 65.3668 34.592C65.3668 31.1389 68.1363 28.352 71.6731 28.352C73.5855 28.352 74.9769 29.0817 76.0038 30.0274L77.6998 28.3715C76.2635 27.0325 74.3528 26.0039 71.6731 26.0039C66.8231 26.0039 62.7471 29.8568 62.7471 34.592C62.7471 39.3273 66.8231 43.1802 71.6731 43.1802C74.2945 43.1802 76.2635 42.3417 77.8113 40.7687C79.3975 39.22 79.8918 37.0425 79.8918 35.2843C79.8918 34.7383 79.8452 34.2362 79.762 33.8169H71.6731ZM132 35.6385C131.532 34.4019 130.089 32.122 127.15 32.122C124.237 32.122 121.812 34.3629 121.812 37.6519C121.812 40.7492 124.212 43.1802 127.43 43.1802C130.031 43.1802 131.532 41.6315 132.15 40.7297L130.219 39.4735C129.575 40.3933 128.698 41.0027 127.43 41.0027C126.17 41.0027 125.266 40.4388 124.687 39.3273L132.261 36.2674L132 35.6385ZM124.277 37.4797C124.212 35.3477 125.973 34.2557 127.235 34.2557C128.224 34.2557 129.062 34.7383 129.342 35.4289L124.277 37.4797ZM118.121 42.8438H120.611V26.5938H118.121V42.8438ZM114.043 33.3538H113.96C113.401 32.707 112.334 32.122 110.982 32.122C108.146 32.122 105.553 34.553 105.553 37.6698C105.553 40.7687 108.146 43.1802 110.982 43.1802C112.334 43.1802 113.401 42.5903 113.96 41.924H114.043V42.717C114.043 44.8312 112.886 45.967 111.021 45.967C109.499 45.967 108.556 44.8945 108.166 43.9927L106.001 44.875C106.625 46.3408 108.278 48.1445 111.021 48.1445C113.94 48.1445 116.403 46.4675 116.403 42.3872V32.4584H114.043V33.3538ZM111.195 41.0027C109.479 41.0027 108.043 39.6003 108.043 37.6698C108.043 35.7214 109.479 34.2995 111.195 34.2995C112.886 34.2995 114.22 35.7214 114.22 37.6698C114.22 39.6003 112.886 41.0027 111.195 41.0027ZM143.657 26.5938H137.702V42.8438H140.185V36.6867H143.657C146.415 36.6867 149.12 34.7383 149.12 31.6394C149.12 28.5422 146.409 26.5938 143.657 26.5938ZM143.722 34.4263H140.185V28.8542H143.722C145.576 28.8542 146.635 30.3573 146.635 31.6394C146.635 32.8972 145.576 34.4263 143.722 34.4263ZM159.073 32.0912C157.279 32.0912 155.413 32.8647 154.646 34.579L156.849 35.4809C157.324 34.579 158.196 34.2865 159.118 34.2865C160.406 34.2865 161.712 35.0422 161.732 36.3763V36.5469C161.283 36.2934 160.321 35.918 159.138 35.918C156.764 35.918 154.346 37.1937 154.346 39.5743C154.346 41.7518 156.29 43.1542 158.475 43.1542C160.146 43.1542 161.068 42.418 161.648 41.5617H161.732V42.8178H164.131V36.5843C164.131 33.7032 161.927 32.0912 159.073 32.0912ZM158.773 40.9962C157.961 40.9962 156.829 40.6029 156.829 39.6198C156.829 38.362 158.241 37.8794 159.462 37.8794C160.556 37.8794 161.068 38.115 161.732 38.4254C161.538 39.9302 160.211 40.9962 158.773 40.9962ZM172.869 32.447L170.014 39.4914H169.93L166.979 32.447H164.301L168.734 42.2913L166.205 47.7692H168.799L175.632 32.447H172.869ZM150.485 42.8438H152.975V26.5938H150.485V42.8438Z"
        fill="#181818"
      />
      <path
        d="M189.636 29.7148L208.636 29.7148M203.636 35.7148L208.929 30.422C209.319 30.0314 209.319 29.3983 208.929 29.0077L203.636 23.7148"
        stroke="#181818"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <defs>
        <linearGradient
          id="paint0_linear_1359_1819"
          x1="35.7496"
          y1="44.6745"
          x2="13.9387"
          y2="22.3352"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#00A0FF" />
          <stop offset="0.0066" stopColor="#00A1FF" />
          <stop offset="0.2601" stopColor="#00BEFF" />
          <stop offset="0.5122" stopColor="#00D2FF" />
          <stop offset="0.7604" stopColor="#00DFFF" />
          <stop offset="1" stopColor="#00E3FF" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_1359_1819"
          x1="51.774"
          y1="29.996"
          x2="19.5558"
          y2="29.996"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFE000" />
          <stop offset="0.4087" stopColor="#FFBD00" />
          <stop offset="0.7754" stopColor="#FFA500" />
          <stop offset="1" stopColor="#FF9C00" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_1359_1819"
          x1="39.7801"
          y1="27.0125"
          x2="10.2028"
          y2="-3.28153"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FF3A44" />
          <stop offset="1" stopColor="#C31162" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_1359_1819"
          x1="16.4393"
          y1="55.769"
          x2="29.6469"
          y2="42.2415"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#32A071" />
          <stop offset="0.0685" stopColor="#2DA771" />
          <stop offset="0.4762" stopColor="#15CF74" />
          <stop offset="0.8009" stopColor="#06E775" />
          <stop offset="1" stopColor="#00F076" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default GooglePlay;
