"use client";

import logo from "@/shared/assets/logo.svg";
import Image from "next/image";
import { PAGES } from "../../shared/consts/pages";
import ChangeTheme from "./components/change-theme";
import ChangeLanguage from "./components/change-language";
import Menu from "./components/menu";
import Link from "next/link";
import { usePathname } from "next/navigation";

const Navbar = () => {
  const pathname = usePathname();

  return (
    <nav className="fixed top-0 left-0 sm:mt-[30px] mt-[10px] w-full flex justify-center z-30 xs:px-5 px-2">
      <div className="bg-[#F3F5F8] shadow-[-2px_14px_31px_0px_rgba(148,148,148,0.102)] dark:bg-[#2A2A2A] md:px-[30px] px-5 rounded-[70px] md:h-[70px] h-[60px] flex items-center justify-between gap-2 max-w-[1024px] w-full">
        <div className="flex items-center gap-[10px]">
          <Image src={logo} alt="Logo" />
          <h3 className="text-[18px] font-semibold">NexLink</h3>
        </div>

        <ul className="lg:flex hidden items-center gap-[30px] flex-1/2 justify-center">
          {PAGES().map((item) => (
            <li
              key={item.name}
              className={`text-[18px] font-semibold ${
                pathname === item.path ? "text-[#B344FF]" : ""
              }`}
            >
              <Link
                prefetch={true}
                href={item.path}
                className="flex items-center gap-[6px]"
              >
                {pathname === item.path ? (
                  <span className="bg-[#B344FF] w-[6px] h-[6px] rounded-full" />
                ) : null}
                {item.name}
              </Link>
            </li>
          ))}
        </ul>

        <div className="flex items-center gap-3.5">
          <ChangeTheme />
          <ChangeLanguage />
          <Menu />
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
