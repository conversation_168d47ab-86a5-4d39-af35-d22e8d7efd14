"use client";

import up_right from "@about-us/assets/up_right.svg";
import Image from "next/image";
import { useState } from "react";
import Card from "./card";
import { Employee } from "./types/TeamProfile";

type Props = {
  name: string;
  team: Employee[];
  isFirst: boolean;
};

const Accordeon = ({ name, team, isFirst }: Props) => {
  const [isOpen, setIsOpen] = useState<boolean>(isFirst ? isFirst : false);
  return (
    <div className="md:py-[30px] py-[11px] flex flex-col md:gap-[30px] gap-[20px] border-solid border-t-[1px] border-b-[1px] border-[#1818181A]">
      <button
        onClick={() => setIsOpen((prev) => !prev)}
        className="flex justify-between items-center w-full"
      >
        <h5 className="font-semibold md:text-[34px] text-[22px] md:leading-[52px] leading-[100%]">
          {name.toUpperCase()}
        </h5>
        <Image src={up_right} alt="Icon" />
      </button>

      {isOpen && (
        <div className="flex gap-4 overflow-hidden overflow-x-auto">
          {team.map((item) => (
            <Card
              key={item.name}
              name={item.name}
              dark_image={item.dark_image}
              light_image={item.light_image}
              position={item.position}
              skills={item.skills}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default Accordeon;
