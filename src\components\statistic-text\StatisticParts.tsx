import React from 'react';

// Core building blocks for statistics
export interface StatisticPartProps {
  children: React.ReactNode;
  className?: string;
}

// Highlighted number or text
export const StatisticHighlight: React.FC<StatisticPartProps> = ({ 
  children, 
  className = "md:text-[64px] text-[40px] md:leading-[50px] leading-[30px] font-semibold" 
}) => (
  <span className={className}>{children}</span>
);

// Regular text
export const StatisticText: React.FC<StatisticPartProps> = ({ 
  children, 
  className = "font-medium md:text-[16px] text-[14px] leading-[130%]" 
}) => (
  <span className={className}>{children}</span>
);

// Container for inline elements (prefix + number)
export const StatisticInline: React.FC<StatisticPartProps> = ({ 
  children, 
  className = "flex items-start flex-wrap md:gap-3 gap-[6px]" 
}) => (
  <div className={className}>{children}</div>
);

// Container for stacked elements (top/bottom layout)
export const StatisticStack: React.FC<StatisticPartProps & { gap?: string }> = ({ 
  children, 
  className = "",
  gap = "md:gap-5 gap-[18px]"
}) => (
  <div className={`flex flex-col ${gap} ${className}`}>{children}</div>
);

// Description text
export const StatisticDescription: React.FC<StatisticPartProps> = ({ 
  children, 
  className = "font-medium md:text-[16px] text-[14px] leading-[130%]" 
}) => (
  <p className={className}>{children}</p>
);
