import Typography from "@/components/typography";
import { setImageToText } from "@/shared/functions/setImageToText";
import { useTranslations } from "next-intl";
import flower from "@about-us/assets/shapes/flower.svg";

const WeSupport = () => {
  const t = useTranslations("aboutUs");
  return (
    <div className="lg:py-[260px] md:py-[160px] py-[100px] px-5 max-w-[1200px] m-auto">
      <Typography variant="h2" className="text-center">
        {setImageToText(t("weSupport"), "От первой идеи", flower, {
          width: 136,
          height: 136,
        })}
      </Typography>
    </div>
  );
};

export default WeSupport;
