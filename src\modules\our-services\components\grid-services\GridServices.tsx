import Card from "./card";
import { useTranslations } from "next-intl";
import landing from "@our-services/assets/grid-services/landing.svg";
import crm from "@our-services/assets/grid-services/crm.svg";
import integration from "@our-services/assets/grid-services/integration.svg";
import blockchain from "@our-services/assets/grid-services/blockchain.svg";

const GridServices = () => {
  const tLanding = useTranslations("ourServices.gridServices.landing");
  const tCrm = useTranslations("ourServices.gridServices.crm");
  const tIntegration = useTranslations("ourServices.gridServices.integration");
  const tBlockchain = useTranslations("ourServices.gridServices.blockchain");
  const tSeo = useTranslations("ourServices.gridServices.seo");
  const tGis = useTranslations("ourServices.gridServices.gis");

  return (
    <div className="mt-[90px] pb-[180px] lg:px-[50px] p-5 grid md:grid-cols-2 grid-cols-1 md:gap-[30px] gap-4">
      <Card
        title={tLanding("title")}
        description={tLanding("description")}
        price={tLanding("price")}
        img={landing}
        isMain
        className="md:col-[1_/_3] col-[1_/_2]"
      />

      <Card
        title={tCrm("title")}
        description={tCrm("description")}
        // price={tCrm("price")}
        img={crm}
      />

      <Card
        title={tIntegration("title")}
        description={tIntegration("description")}
        price={tIntegration("price")}
        img={integration}
      />

      <Card
        title={tBlockchain("title")}
        description={tBlockchain("description")}
        // price={tBlockchain("price")}
        img={blockchain}
      />

      <div className="flex flex-col md:gap-[30px] gap-4">
        <Card
          title={tSeo("title")}
          description={tSeo("description")}
          // price={tSeo("price")}
          className="grow"
        />
        <Card
          title={tGis("title")}
          description={tGis("description")}
          price={tGis("price")}
          className="grow"
        />
      </div>
    </div>
  );
};

export default GridServices;
