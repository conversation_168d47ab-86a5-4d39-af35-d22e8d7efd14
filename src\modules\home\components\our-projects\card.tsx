import { useTranslations } from "next-intl";
import Image, { StaticImageData } from "next/image";
import React from "react";

type Props = {
  img: StaticImageData;
  title: string;
  date: string;
  mainImage: StaticImageData;
  description: string;
  link: string;
};

const Card = ({ img, title, date, mainImage, description, link }: Props) => {
  const t = useTranslations("home.ourProjects");
  return (
    <div className="p-6 flex flex-col md:gap-[23px] gap-[12px] bg-white dark:bg-[#444444] rounded-[30px]">
      <div className="flex items-center md:gap-[22px] gap-[14px]">
        <Image
          className="md:w-[78px] md:h-[78px] w-[62px] h-[62px] rounded-full"
          src={img}
          alt="Image"
        />
        <div className="flex flex-col md:gap-[6px] gap-[4px]">
          <h5 className="md:text-[18px] text-[16px] font-bold leading-[110%] text-[#090909] dark:text-white">
            {title}
          </h5>
          <p className="text-[13px] font-medium leading-[110%] text-[#090909] dark:text-white">
            {date}
          </p>
        </div>
      </div>

      <Image
        src={mainImage}
        alt="Main Image"
        className="w-full h-[210px] md:rounded-[93px] rounded-[60px] object-cover"
      />

      <div className="flex flex-col gap-9">
        <p className="text-[16px] font-medium leading-[130%]">{description}</p>
        <span className="text-[16px] font-medium leading-[130%] flex items-center flex-wrap gap-x-[20px] gap-y-[10px]">
          {t("link")}{" "}
          <span className="bg-[#EEEBFF] dark:bg-[#403D51] border-[#EEEBFF] dark:border-[#403D51] border-solid border-[1px] px-4 py-[9px] rounded-[26px]">
            <a href="#" target="_blank" rel="noopener noreferrer">
              {link}
            </a>
          </span>
        </span>
      </div>
    </div>
  );
};

export default Card;
