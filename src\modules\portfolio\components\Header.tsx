import { useTranslations } from "next-intl";
import React from "react";
import { PAGES } from "../consts/pages";
import Tabs from "./Tabs";

const Header = () => {
  const t = useTranslations("portfolio");
  const pages = PAGES();

  console.log(pages);

  return (
    <div className="mt-[250px] flex flex-col items-center md:gap-[60px] gap-[50px]">
      <h2 className="md:text-[104px] sm:text-[64px] text-[54px] font-semibold md:leading-[84px] leading-[100%]">
        {t("title")}
      </h2>

      <Tabs items={pages} />
    </div>
  );
};

export default Header;
