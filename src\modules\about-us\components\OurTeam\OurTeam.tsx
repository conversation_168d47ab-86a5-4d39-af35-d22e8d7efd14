import Typography from "@/components/typography";
import { getTranslations } from "next-intl/server";
import { getEmployees } from "./api/getEmployees";
import Accordeon from "./accordeon";

const OurTeam = async () => {
  const t = await getTranslations("aboutUs");

  const data = (await getEmployees()) ?? [];

  return (
    <div className="flex flex-col gap-[30px] lg:px-[50px] p-5">
      <div className="flex justify-between lg:gap-4 gap-5 flex-wrap">
        <Typography variant="h4">{t("title")}</Typography>
        <Typography variant="p">{t("description")}</Typography>
      </div>

      <div>
        {data?.map((item, i) => (
          <Accordeon
            key={item.direction}
            team={item.team}
            name={item.direction}
            isFirst={i === 0}
          />
        ))}
      </div>
    </div>
  );
};

export default OurTeam;
