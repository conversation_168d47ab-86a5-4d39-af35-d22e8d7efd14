import {
  Header,
  TrustUs,
  OurServices,
  FreshDecisions,
  AboutCompany,
  // OurProjects,
} from "@home/components";

type Props = {
  params: Promise<{
    locale: string;
  }>;
};

const Home = async ({ params }: Props) => {
  const { locale } = await params;
  return (
    <div className="pb-[100px]">
      <Header />

      <TrustUs />

      <OurServices />

      <FreshDecisions locale={locale} />

      <AboutCompany />

      {/* <OurProjects /> */}
    </div>
  );
};

export default Home;
