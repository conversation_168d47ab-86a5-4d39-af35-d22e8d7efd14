import Image from "next/image";
import statistics_image from "@portfolio/assets/statistic_image.svg";
import statistics_rating from "@portfolio/assets/statistic_rating.svg";
import statistics_shape from "@portfolio/assets/statistic_shape.svg";

const ExampleStatistics2 = () => {
  return (
    <div className="grid lg:grid-cols-4 sm:grid-cols-2 grid-cols-1 md:gap-4 gap-[10px]">
      <div className="bg-white dark:bg-[#434343] md:p-[30px] p-[14px] rounded-[25px] flex flex-col justify-between items-end md:h-[350px] h-[280px]">
        <Image
          src={statistics_shape}
          alt="Shape"
          className="md:w-[102px] md:h-[92px] w-[64px] h-[58px]"
        />

        <div className="flex flex-col md:gap-5 gap-[18px]">
          <h4 className="font-medium text-[16px] leading-[130%] flex items-start flex-wrap md:gap-3 gap-[6px]">
            Более{" "}
            <span className="font-semibold md:text-[64px] text-[40px] md:leading-[50px] leading-[30px]">
              200+
            </span>
          </h4>
          <p className="font-medium md:text-[16px] text-[14px] leading-[130%]">
            довольных клиентов, уже доверивших нам своё приключение.
          </p>
        </div>
      </div>

      <div className="bg-white dark:bg-[#434343] md:p-[30px] p-[14px] rounded-[25px] flex flex-col justify-between md:h-[350px] h-[280px]">
        <h4 className="font-medium text-[16px] leading-[130%] flex items-start flex-wrap md:gap-3 gap-[6px]">
          Более{" "}
          <span className="font-semibold md:text-[64px] text-[40px] md:leading-[50px] leading-[30px]">
            15
          </span>
        </h4>

        <p className="font-medium md:text-[16px] text-[14px] leading-[130%]">
          направлений природных экскурсий и не только!
        </p>
      </div>

      <Image
        src={statistics_image}
        alt="Image"
        className="rounded-[145px] w-full md:h-[350px] h-[280px] object-cover"
      />

      <div className="bg-white dark:bg-[#434343] md:p-[30px] p-[14px] rounded-[25px] flex flex-col justify-between md:h-[350px] h-[280px]">
        <h4 className="font-medium text-[16px] leading-[130%] flex items-start flex-wrap md:gap-3 gap-[6px]">
          Ежедневные
          <span className="font-semibold md:text-[64px] text-[40px] md:leading-[50px] leading-[30px]">
            туры
          </span>
        </h4>

        <p className="font-medium md:text-[16px] text-[14px] leading-[130%]">
          с комфортом и по доступной цене{" "}
        </p>
      </div>
    </div>
  );
};

export default ExampleStatistics2;
