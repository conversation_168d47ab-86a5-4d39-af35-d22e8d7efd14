import Close from "@/shared/assets/close";
import { useLocale, useTranslations } from "next-intl";
import Typography from "../typography";
import Input from "../input";
import Button from "../button";
import { useEffect, useState } from "react";
import { FeedbackPayload } from "@/shared/types";
import { sendForm } from "@/shared/api";
import { addToast } from "@heroui/toast";
import { Checkbox, ModalContent, Spinner } from "@heroui/react";
import { OfferingList } from "./types/OfferingList";
import api from "@/shared/api-config/axios-config";
import { Modal } from "@heroui/react";

type Props = {
  handleClose: () => void;
  isOpen: boolean;
};

const FormModal = ({ handleClose, isOpen }: Props) => {
  const t = useTranslations("common");
  const [errors, setErrors] = useState<Partial<
    Record<keyof FeedbackPayload, string>
  > | null>(null);
  const [feedback, setFeedback] = useState<FeedbackPayload>({
    name: "",
    email: "",
    phone_number: "",
    message: "",
  });
  const [selectedOfferings, setSelectedOfferings] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [offerings, setOfferings] = useState<OfferingList>([]);
  const lang = useLocale();
  console.log(lang);

  const handleFeedbackChanges = (
    e: React.ChangeEvent<HTMLInputElement>,
    fieldname: keyof FeedbackPayload
  ) => {
    setFeedback((prev) => ({ ...prev, [fieldname]: e.target.value }));
  };

  const handleForm = async () => {
    if (!feedback) {
      setErrors({ message: "Заполните все необходимые поля!" });
    }

    if (!feedback?.name.trim()) {
      setErrors({ name: "Введите ваше имя!" });
    }

    if (
      !feedback?.email ||
      !feedback?.email.trim() ||
      !feedback?.phone_number ||
      !feedback?.phone_number.trim()
    ) {
      setErrors({ phone_number: "Заполните E-Mail или номер телефона!" });
    }

    if (!feedback?.message) {
      setErrors({ message: "Введите сообщение!" });
    }

    setErrors(null);
    setIsLoading(true);

    const res = await sendForm({ ...feedback, offerings: selectedOfferings });

    if (res.status === "error") {
      setErrors({ message: res.message });
    }

    if (res.status === "success") {
      setFeedback({ name: "", email: "", phone_number: "", message: "" });
      setSelectedOfferings([]);
      addToast({ title: res.message, color: "success" });
      handleClose();
    }

    setIsLoading(false);
  };

  const getOfferings = async () => {
    const offerings = await api.get<OfferingList>("feedback_offering/client", {
      headers: {
        "accept-language": lang,
      },
    });

    setOfferings(offerings.data);
  };

  useEffect(() => {
    // Добавляем getOfferings в массив зависимостей
    getOfferings();
  }, []);

  const selectOffering = (id: string) => {
    setSelectedOfferings((prev) => {
      if (prev.includes(id)) {
        return prev.filter((item) => item !== id);
      }

      return [...prev, id];
    });
  };

  return (
    <Modal
      backdrop="blur"
      scrollBehavior="inside"
      size="4xl"
      onClose={handleClose}
      isOpen={isOpen}
      hideCloseButton
      placement="top-center"
    >
      <ModalContent>
        <div
          onClick={(e) => e.stopPropagation()}
          className="bg-[#F3F5F8] dark:bg-[#2A2A2A] max-w-[910px] w-full flex flex-col gap-[37px] p-[30px] rounded-[30px]"
        >
          <div className="flex items-center justify-between">
            <Typography variant="h5">{t("feedback")}</Typography>
            <button onClick={handleClose}>{<Close />}</button>
          </div>

          <div className="flex flex-col gap-5">
            <Input
              value={feedback.name}
              error={errors?.name}
              onChange={(e) => handleFeedbackChanges(e, "name")}
              placeholder={t("name")}
              name="name"
            />
            <Input
              value={feedback.email}
              error={errors?.email}
              onChange={(e) => handleFeedbackChanges(e, "email")}
              placeholder="E-Mail"
              name="email"
            />
            <div className="px-[6px] py-[10px] bg-[white] rounded-[16px] grid grid-cols-2 gap-x-[10px] gap-y-[6px]">
              {offerings.map((item) => (
                <div
                  key={item.id}
                  className="px-[10px] py-[6px] flex items-center gap-2"
                >
                  <Checkbox
                    checked={selectedOfferings.includes(item.id)}
                    onChange={() => selectOffering(item.id)}
                  />
                  <p className="text-[18px] leading-[20px]">{item.name}</p>
                </div>
              ))}
            </div>
            <Input
              value={feedback.phone_number}
              error={errors?.phone_number}
              onChange={(e) => handleFeedbackChanges(e, "phone_number")}
              placeholder={t("phoneNumber")}
              name="phone_number"
            />
            <Input
              value={feedback.message}
              error={errors?.message}
              onChange={(e) => handleFeedbackChanges(e, "message")}
              placeholder={t("yourMessage")}
              name="message"
            />
            <Button onPress={handleForm} className="mt-[10px]">
              {isLoading ? <Spinner color="current" /> : t("send")}
            </Button>
          </div>
        </div>
      </ModalContent>
    </Modal>
  );
};

export default FormModal;
