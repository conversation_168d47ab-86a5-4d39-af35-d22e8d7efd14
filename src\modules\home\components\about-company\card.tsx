import Image, { StaticImageData } from "next/image";

type Props = {
  title: string;
  description: string;
  img: StaticImageData | React.ReactNode;
  isLast?: boolean;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const isStaticImageData = (img: any): img is StaticImageData =>
  typeof img === "object" &&
  img !== null &&
  "src" in img &&
  typeof (img as StaticImageData).src === "string";

const Card = ({ title, description, img, isLast }: Props) => {
  const bg = isLast
    ? "bg-[#EEEBFF] dark:bg-[#403D51]"
    : "bg-[#F3F5F8] dark:bg-[#2A2A2A]";

  return (
    <div
      className={`md:p-6 p-4 flex flex-col items-end md:gap-14 gap-[61px] ${bg} rounded-[20px]`}
    >
      {isStaticImageData(img) ? (
        <Image src={img} alt={title} />
      ) : (
        // если это React-элемент (или любой другой узел), просто рендерим его
        <div className="relative flex justify-end h-[56px] w-full">{img}</div>
      )}
      <div className="w-full flex flex-col gap-7">
        <h5 className="md:text-[64px] text-[48px] leading-[100%] font-semibold">
          {title}
        </h5>
        <p className="text-[16px] leading-[130%] font-medium">{description}</p>
      </div>
    </div>
  );
};

export default Card;
