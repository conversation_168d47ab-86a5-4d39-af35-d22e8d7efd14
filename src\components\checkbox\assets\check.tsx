"use client";

import { useCheckMount } from "@/shared/hooks/useCheckMount";
import { useTheme } from "next-themes";

const Check = () => {
  const { theme } = useTheme();
  const isMounted = useCheckMount();

  if (!isMounted) return null;
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.83634 12.6942L8.33183 12.1393L8.83634 12.6942ZM14.5045 8.55496C14.811 8.27632 14.8336 7.80199 14.555 7.4955C14.2763 7.189 13.802 7.16641 13.4955 7.44504L14.5045 8.55496ZM8.16366 12.6942L8.66817 12.1393L8.16366 12.6942ZM6.5045 10.1723C6.19801 9.89369 5.72368 9.91628 5.44504 10.2228C5.16641 10.5293 5.189 11.0036 5.4955 11.2822L6.5045 10.1723ZM3.18404 18.564L3.52453 17.8958L3.18404 18.564ZM1.43597 16.816L2.10423 16.4755L1.43597 16.816ZM18.564 16.816L17.8958 16.4755L18.564 16.816ZM16.816 18.564L16.4755 17.8958L16.816 18.564ZM16.816 1.43597L16.4755 2.10423L16.816 1.43597ZM18.564 3.18404L17.8958 3.52453L18.564 3.18404ZM3.18404 1.43597L3.52453 2.10423L3.18404 1.43597ZM1.43597 3.18404L2.10423 3.52453L1.43597 3.18404ZM9.34084 13.2492L14.5045 8.55496L13.4955 7.44504L8.33183 12.1393L9.34084 13.2492ZM8.66817 12.1393L6.5045 10.1723L5.4955 11.2822L7.65916 13.2492L8.66817 12.1393ZM8.33183 12.1393C8.42719 12.0526 8.57281 12.0526 8.66817 12.1393L7.65916 13.2492C8.13594 13.6826 8.86406 13.6826 9.34084 13.2492L8.33183 12.1393ZM7.4 1.75H12.6V0.25H7.4V1.75ZM18.25 7.4V12.6H19.75V7.4H18.25ZM12.6 18.25H7.4V19.75H12.6V18.25ZM1.75 12.6V7.4H0.25V12.6H1.75ZM7.4 18.25C6.26752 18.25 5.46326 18.2494 4.83388 18.198C4.21325 18.1473 3.829 18.0509 3.52453 17.8958L2.84355 19.2323C3.39472 19.5131 3.99834 19.6347 4.71173 19.693C5.41637 19.7506 6.29227 19.75 7.4 19.75V18.25ZM0.25 12.6C0.25 13.7077 0.249417 14.5836 0.306988 15.2883C0.365274 16.0017 0.486882 16.6053 0.767719 17.1565L2.10423 16.4755C1.94909 16.171 1.85271 15.7867 1.80201 15.1661C1.75058 14.5367 1.75 13.7325 1.75 12.6H0.25ZM3.52453 17.8958C2.913 17.5842 2.41582 17.087 2.10423 16.4755L0.767719 17.1565C1.22312 18.0502 1.94978 18.7769 2.84355 19.2323L3.52453 17.8958ZM18.25 12.6C18.25 13.7325 18.2494 14.5367 18.198 15.1661C18.1473 15.7867 18.0509 16.171 17.8958 16.4755L19.2323 17.1565C19.5131 16.6053 19.6347 16.0017 19.693 15.2883C19.7506 14.5836 19.75 13.7077 19.75 12.6H18.25ZM12.6 19.75C13.7077 19.75 14.5836 19.7506 15.2883 19.693C16.0017 19.6347 16.6053 19.5131 17.1565 19.2323L16.4755 17.8958C16.171 18.0509 15.7867 18.1473 15.1661 18.198C14.5367 18.2494 13.7325 18.25 12.6 18.25V19.75ZM17.8958 16.4755C17.5842 17.087 17.087 17.5842 16.4755 17.8958L17.1565 19.2323C18.0502 18.7769 18.7769 18.0502 19.2323 17.1565L17.8958 16.4755ZM12.6 1.75C13.7325 1.75 14.5367 1.75058 15.1661 1.80201C15.7867 1.85271 16.171 1.94909 16.4755 2.10423L17.1565 0.767719C16.6053 0.486882 16.0017 0.365274 15.2883 0.306988C14.5836 0.249417 13.7077 0.25 12.6 0.25V1.75ZM19.75 7.4C19.75 6.29227 19.7506 5.41637 19.693 4.71173C19.6347 3.99834 19.5131 3.39472 19.2323 2.84355L17.8958 3.52453C18.0509 3.829 18.1473 4.21325 18.198 4.83388C18.2494 5.46326 18.25 6.26752 18.25 7.4H19.75ZM16.4755 2.10423C17.087 2.41582 17.5842 2.913 17.8958 3.52453L19.2323 2.84355C18.7769 1.94978 18.0502 1.22312 17.1565 0.767719L16.4755 2.10423ZM7.4 0.25C6.29227 0.25 5.41637 0.249417 4.71173 0.306988C3.99834 0.365274 3.39472 0.486882 2.84355 0.767719L3.52453 2.10423C3.829 1.94909 4.21325 1.85271 4.83388 1.80201C5.46326 1.75058 6.26752 1.75 7.4 1.75V0.25ZM1.75 7.4C1.75 6.26752 1.75058 5.46326 1.80201 4.83388C1.85271 4.21325 1.94909 3.829 2.10423 3.52453L0.767719 2.84355C0.486882 3.39472 0.365274 3.99834 0.306988 4.71173C0.249417 5.41637 0.25 6.29227 0.25 7.4H1.75ZM2.84355 0.767719C1.94978 1.22312 1.22312 1.94978 0.767719 2.84355L2.10423 3.52453C2.41582 2.913 2.913 2.41582 3.52453 2.10423L2.84355 0.767719Z"
        fill={theme === "dark" ? "white" : "black"}
      />
    </svg>
  );
};

export default Check;
