import React from "react";
import { Statistics } from "@portfolio/components";
import ExampleStatistics1 from "../components/ExampleStatistics1";
import ExampleStatistics2 from "../components/ExampleStatistics2";
import TestStatistics from "../components/TestStatistics";

const Product = () => {
  return (
    <div className="flex flex-col gap-[60px]">
      <div className="text-center">
        <h1 className="text-[48px] font-bold mb-4">
          Portfolio Statistics Demo
        </h1>
        <p className="text-[18px] text-gray-600">
          Демонстрация различных макетов статистики
        </p>
      </div>

      <div className="space-y-12">
        <div>
          <h2 className="text-2xl font-semibold mb-4">New Enhanced System</h2>

          <div className="space-y-8">
            <div>
              <h3 className="text-lg font-medium mb-2">
                iZDE Project (Example 1 Pattern)
              </h3>
              <p className="text-gray-600 mb-4">
                Number at bottom, inline with prefix, number at bottom with icon
              </p>
              <Statistics projectKey="izde" />
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">
                iZDETour Project (Mixed Pattern)
              </h3>
              <p className="text-gray-600 mb-4">
                Number at top, inline, word highlighting
              </p>
              <Statistics projectKey="izdetour" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Product;
