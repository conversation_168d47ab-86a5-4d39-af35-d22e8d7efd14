import Typography from "@/components/typography";
import { getTranslations } from "next-intl/server";
import CardImage from "./card-image";
import CardIcons from "./card-icons";
import CardCustom from "./card-custom";
import automatization from "@home/assets/services/automatization.svg";
import swift from "@home/assets/services/swift.svg";
import kotlin from "@home/assets/services/kotlin.svg";
import flutter from "@home/assets/services/flutter.svg";
import design from "@home/assets/services/design.svg";
import crm from "@home/assets/services/crm.svg";
import database from "@home/assets/services/database.svg";
import integration from "@home/assets/services/integration.svg";
import javascript from "@home/assets/services/javascript.svg";
import python from "@home/assets/services/python.svg";

const OurServices = async () => {
  const t = await getTranslations("home.services");
  return (
    <div className="mt-[180px] flex flex-col gap-[45px]">
      <div className="px-5 flex flex-col items-center gap-4 text-center">
        <Typography className="max-w-[874px]" variant="h4">
          {t("title")}
        </Typography>
        <Typography className="max-w-[808px]" variant="p">
          {t("description")}
        </Typography>
      </div>

      <div className="lg:px-[50px] px-5 grid lg:grid-cols-4 md:grid-cols-2 grid-cols-1 gap-4">
        <div className="flex flex-col gap-4">
          <CardImage
            title={t("automatization.title")}
            description={t("automatization.description")}
            img={automatization}
          />
          <CardIcons
            title={t("mobileDevelopment.title")}
            description={t("mobileDevelopment.description")}
            icons={[kotlin, swift, flutter]}
          />
        </div>

        <div className="flex flex-col gap-4">
          <CardCustom
            title={t("design.title")}
            description={t("design.description")}
            img={design}
          />

          <CardImage
            title={t("seo.title")}
            description={t("seo.description")}
          />

          <CardImage
            reverse
            title={t("crm.title")}
            description={t("crm.description")}
            img={crm}
          />
        </div>

        <div className="flex flex-col gap-4">
          <CardImage
            title={t("databases.title")}
            description={t("databases.description")}
            img={database}
          />

          <CardImage
            title={t("systemIntegration.title")}
            description={t("systemIntegration.description")}
            img={integration}
          />
        </div>

        <div className="flex flex-col gap-4">
          <CardIcons
            title={t("webDevelopment.title")}
            description={t("webDevelopment.description")}
            icons={[javascript, python]}
          />

          <CardImage
            title={t("turnkey.title")}
            description={t("turnkey.description")}
          />
        </div>
      </div>
    </div>
  );
};

export default OurServices;
