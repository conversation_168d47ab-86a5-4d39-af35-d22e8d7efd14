interface Props extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string | undefined;
}

const Input = ({ error, ...rest }: Props) => {
  return (
    <div className="flex flex-col gap-1">
      <input
        className="outline-none border-b-[1px] border-solid border-[#1818181A] dark:border-[#ffffff] h-[49px] placeholder:text-[#181818] dark:placeholder:text-[#ffffff] placeholder:font-medium"
        {...rest}
        type="text"
      />
      {error ? <p className="text-red-700">{error}</p> : null}
    </div>
  );
};

export default Input;
