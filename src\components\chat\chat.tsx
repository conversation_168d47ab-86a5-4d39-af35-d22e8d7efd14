"use client";

import Image from "next/image";
import message from "./assets/message.svg";
import logo from "./assets/logo.svg";
import cross from "./assets/cross.svg";
import { useEffect, useRef, useState } from "react";
import { QuestionAnswerItem } from "./types/QuestionAnswer";
import Button from "../button";
import list_icon from "./assets/list_icon.svg";
import arrow_left from "./assets/arrow-left.svg";
import arrow_right from "./assets/arrow-right.svg";
import useClickOutside from "@/shared/hooks/useClickOutside";
import { useLocale, useTranslations } from "next-intl";
import Question from "./components/question";
import bot from "./assets/bot.svg";
import { useTypewriter } from "@/shared/hooks/useTypeWriter";
import api from "@/shared/api-config/axios-config";
import { Skeleton } from "@heroui/react";

const Chat = () => {
  const t = useTranslations("common.chat");
  const chatRef = useRef<HTMLDivElement>(null);
  const [isOpenChat, setIsOpenChat] = useState<boolean>(false);
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [showAllQuestions, setShowAllQuestions] = useState<boolean>(false);
  const [questionAnswers, setQuestionAnswers] = useState<{
    next: boolean;
    prev: boolean;
    results: QuestionAnswerItem[];
  }>({ next: false, prev: false, results: [] });
  const [selectedQuestion, setSelectedQuestion] =
    useState<QuestionAnswerItem | null>(null);
  const answer = selectedQuestion?.answer ?? "";
  const { displayed: typedAnswer, isTyping } = useTypewriter(answer, 30);
  const title = t("title");
  const lang = useLocale();
  const [page, setPage] = useState<number>(1);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const getQuestionAnswers = async () => {
    setIsLoading(true);
    const response = await api.get(`answer_question/client?page=${page}`, {
      headers: {
        "accept-language": lang,
      },
    });
    setQuestionAnswers(response.data);
    setIsLoading(false);
  };

  useEffect(() => {
    // Добавляем getQuestionAnswers в массив зависимостей
    getQuestionAnswers();
  }, []);

  useClickOutside(chatRef, () => {
    setIsOpenChat(false);
    setShowAllQuestions(false);
    setSelectedQuestion(null);
  });

  const nextPage = () => {
    setPage((prev) => prev + 1);
  };

  const prevPage = () => {
    setPage((prev) => prev - 1);
  };

  return (
    <div className="fixed bottom-[20px] md:right-[50px] right-[20px] z-[200]">
      <button
        onClick={() => setIsOpenChat(true)}
        onMouseEnter={() => setIsFocused(true)}
        onMouseLeave={() => setIsFocused(false)}
        className={`w-[78px] h-[78px] rounded-full bg-[#F3F5F8] flex items-center justify-center border-[1px solid #1818181A] shadow-2xl ${
          isFocused ? "" : "animate-tiny-bounce pointer-events-auto"
        }`}
      >
        <Image src={message} alt="Message Icon" />
      </button>

      <div
        ref={chatRef}
        className={`rounded-[30px] overflow-hidden bg-[#FFFFFF] dark:bg-[#181818] shadow-2xl fixed bottom-[0px] right-[0px] md:right-[50px] md:bottom-[20px] md:w-[440px] w-full ${
          isOpenChat
            ? "animate-appearance-in pointer-events-auto"
            : "animate-appearance-out opacity-0 pointer-events-none"
        }`}
      >
        <div className="py-[20px] px-[24px] flex items-center justify-between border-b-[1px] border-b-[#E3E3E3B2] dark:border-b-[#343434] border-solid">
          <div className="flex items-center gap-[10px]">
            <Image src={logo} alt="Logo" />
            <span className="text-[18px] font-semibold leading-[100%]">
              NexLink
            </span>
          </div>

          <button
            onClick={() => {
              setIsOpenChat(false);
              setShowAllQuestions(false);
              setSelectedQuestion(null);
              setPage(1);
            }}
          >
            <Image src={cross} alt="Cross Icon" />
          </button>
        </div>

        <div className="md:max-h-[70vh] max-h-[88vh] overflow-hidden overflow-y-auto">
          {selectedQuestion ? (
            // 3) Отображаем выбранный вопрос (всегда приоритетнее)
            <div className="animate-appearance-in flex flex-col items-center gap-[80px]">
              <div className="w-full">
                <div className="p-[30px] flex items-center gap-4">
                  <Image src={bot} alt="Bot Image" />
                  <span className="text-[16px] font-semibold leading-[19px]">
                    {selectedQuestion?.question}
                  </span>
                </div>

                <div className="flex flex-col gap-[20px] px-[30px] py-[20px] bg-[#F3F5F8] dark:bg-[#2A2A2A] w-full">
                  <Image src={logo} alt="Logo" />

                  <span
                    className={`text-[15px] font-medium leading-[145%] ${
                      isTyping ? "animate-blink" : ""
                    }`}
                  >
                    {typedAnswer}
                  </span>
                </div>
              </div>

              <Button
                onPress={() => {
                  setShowAllQuestions(true);
                  setSelectedQuestion(null);
                  setPage(1);
                }}
                className="font-semibold w-fit mb-[40px]"
              >
                {t("showAll")} <Image src={list_icon} alt="List Icon" />
              </Button>
            </div>
          ) : showAllQuestions ? (
            // 2) Показать весь список
            <div className="p-[30px] pb-[38px] animate-appearance-in">
              <div className="flex flex-col gap-[48px]">
                <div className="flex flex-col gap-[10px] max-h-[400px] overflow-y-auto">
                  {isLoading
                    ? Array.from({ length: 5 }, (_, i) => (
                        <Skeleton key={i} className="rounded-lg h-[30px]" />
                      ))
                    : questionAnswers.results.slice(0, 5).map((item) => (
                        <Question
                          key={item.id}
                          onPress={() => setSelectedQuestion(item)}
                        >
                          {item.question}
                        </Question>
                      ))}
                </div>
                <div className="flex justify-center gap-[40px]">
                  <Button
                    isDisabled={!questionAnswers.prev}
                    className="min-w-[45px] max-w-[45px] h-[45px]"
                    onPress={() => {
                      prevPage();
                    }}
                  >
                    <Image
                      className="min-w-[24px] h-[24px]"
                      src={arrow_left}
                      alt="Arrow Left"
                    />
                  </Button>
                  <Button
                    isDisabled={!questionAnswers.next}
                    className="min-w-[45px] max-w-[45px] h-[45px]"
                    onPress={() => {
                      nextPage();
                    }}
                  >
                    <Image
                      className="min-w-[24px] h-[24px]"
                      src={arrow_right}
                      alt="Arrow Right"
                    />
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            // 1) Начальный экран — первые 3 вопроса
            <div className="mt-[100px] px-[30px] pb-[40px] flex flex-col items-center gap-[80px] animate-appearance-in">
              <h3
                className="text-[36px] font-semibold text-center typewriter"
                style={{ "--chars": `${title.length}` } as React.CSSProperties}
              >
                {title}
              </h3>
              <div className="flex flex-col gap-4 w-full max-h-[200px]">
                {isLoading
                  ? Array.from({ length: 3 }, (_, i) => (
                      <Skeleton key={i} className="rounded-lg h-[30px]" />
                    ))
                  : questionAnswers.results.slice(0, 3).map((item) => (
                      <Question
                        key={item.id}
                        onPress={() => setSelectedQuestion(item)}
                      >
                        {item.question}
                      </Question>
                    ))}
              </div>
              <Button
                onPress={() => {
                  setShowAllQuestions(true);
                  setPage(1);
                }}
                className="font-semibold w-fit"
              >
                {t("showAll")} <Image src={list_icon} alt="List Icon" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Chat;
