import { Button as <PERSON>Butt<PERSON> } from "@heroui/button";
import { ButtonProps } from "@heroui/react";
import Image from "next/image";
import arrow from "./assets/arrow_up_right.svg";

const OrderButton = ({ children, ...rest }: ButtonProps) => {
  return (
    <HeroButton
      className="pl-[18px] pr-[7px] h-[44px] flex items-center gap-[18px] bg-[linear-gradient(90deg,_#B344FF_0%,_rgba(179,68,255,0.7)_50%,_#D89CFF_100%)] rounded-[42px]"
      {...rest}
    >
      <span className="text-[16px] font-medium leading-[100%] text-white">
        {children}
      </span>
      <div className="w-[34px] h-[34px] bg-white rounded-full flex items-center justify-center">
        <Image src={arrow} alt="Arrow up right" />
      </div>
    </HeroButton>
  );
};

export default OrderButton;
