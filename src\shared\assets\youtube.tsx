"use client";

import { useCheckMount } from "@/shared/hooks/useCheckMount";
import { useTheme } from "next-themes";

const Youtube = () => {
  const { theme } = useTheme();
  const isMounted = useCheckMount();

  if (!isMounted) return null;

  return (
    <svg
      width="41"
      height="41"
      viewBox="0 0 41 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="1"
        y="1"
        width="39"
        height="39"
        rx="19.5"
        stroke={theme === "dark" ? "#ffffff" : "#181818"}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.7625 27.5568C11.3702 27.4408 11.0123 27.2343 10.719 26.9549C10.4256 26.6754 10.2054 26.3312 10.0769 25.9512C9.34292 23.9874 9.12543 15.7869 10.5391 14.1681C11.0096 13.6414 11.6739 13.3172 12.3878 13.2658C16.1802 12.8677 27.8975 12.9208 29.2432 13.3985C29.6217 13.5183 29.9675 13.7203 30.2547 13.9891C30.5419 14.258 30.763 14.5868 30.9015 14.951C31.7035 16.9812 31.7307 24.3589 30.7928 26.3095C30.544 26.8173 30.1275 27.2285 29.6102 27.4772C28.1965 28.1672 13.6383 28.1539 11.7625 27.5568ZM17.7842 23.7087L24.5808 20.2587L17.7842 16.7821V23.7087Z"
        fill={theme === "dark" ? "#ffffff" : "#181818"}
      />
    </svg>
  );
};

export default Youtube;
