import { StaticImageData } from "next/image";
import React from "react";

type Size = { width: number; height: number };

export const setImageToText = (
  text: string,
  part: string | string[],
  image: StaticImageData | StaticImageData[],
  size: Size | Size[]
): React.ReactNode => {
  // Нормализуем входные данные к массивам
  const partsArray = Array.isArray(part) ? part : [part];
  const getImage = (index: number): StaticImageData =>
    Array.isArray(image) ? image[index] : image;
  const getSize = (index: number): Size =>
    Array.isArray(size) ? size[index] : size;

  const result: React.ReactNode[] = [];
  let currentIndex = 0;

  // Для каждого элемента массива parts ищем его первое вхождение,
  // начиная с текущего индекса
  for (let i = 0; i < partsArray.length; i++) {
    const search = partsArray[i];
    const foundIndex = text.indexOf(search, currentIndex);
    if (foundIndex === -1) {
      // Если элемент не найден, можно просто пропустить его
      continue;
    }
    // Добавляем текст до найденного элемента
    if (foundIndex > currentIndex) {
      result.push(text.substring(currentIndex, foundIndex));
    }
    // Оборачиваем найденный текст в span, которому задаём CSS-переменные
    // для изображения, ширины и высоты
    result.push(
      <span
        key={`with-image-${i}`}
        className="with-image"
        style={
          {
            "--image-url": `url(${getImage(i).src})`,
            "--image-width": `${getSize(i).width}px`,
            "--image-height": `${getSize(i).height}px`,
          } as React.CSSProperties
        }
      >
        {search}
      </span>
    );
    currentIndex = foundIndex + search.length;
  }
  // Добавляем оставшийся текст
  if (currentIndex < text.length) {
    result.push(text.substring(currentIndex));
  }

  return <>{result}</>;
};
