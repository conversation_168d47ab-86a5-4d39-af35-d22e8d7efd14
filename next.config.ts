import { NextConfig } from "next";
import createNextIntlPlugin from "next-intl/plugin";
import withPlaiceholder from "@plaiceholder/next";

const nextConfig: NextConfig = {
  experimental: {
    staleTimes: {
      dynamic: 60,
      static: 180,
    },
    useCache: true,
    optimizeCss: {
      critters: {
        ssrMode: "sync", // Может помочь с проблемами сборки
      },
    },
    scrollRestoration: true,
    optimizePackageImports: [
      "framer-motion",
      "@heroui/button",
      "@heroui/modal",
      "@heroui/react",
      "@heroui/skeleton",
      "@heroui/system",
      "@heroui/theme",
      "@heroui/toast",
    ],
  },
  onDemandEntries: {
    maxInactiveAge: 60 * 60 * 1000, // 1 hour
    pagesBufferLength: 10,
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "nexlink-web-static.s3.eu-central-1.amazonaws.com",
        port: "",
        pathname: "/**",
      },
    ],
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === "production",
  },
};

const withNextIntl = createNextIntlPlugin();

export default withNextIntl(withPlaiceholder(nextConfig));
