import { useTranslations } from "next-intl";
import automatization from "@our-services/assets/list-services/automatization.svg";
import design from "@our-services/assets/list-services/design.svg";
import mobile from "@our-services/assets/list-services/mobile.svg";

export const LIST_SERVICES = () => {
  const tAutomatization = useTranslations(
    "ourServices.listServices.automatization"
  );
  const tDesign = useTranslations("ourServices.listServices.design");
  const tMobile = useTranslations("ourServices.listServices.mobileDevelopment");

  const automatizationArray = JSON.parse(
    tAutomatization("includes").replace(/'/g, '"')
  );

  const designArray = JSON.parse(tDesign("includes").replace(/'/g, '"'));

  const mobileArray = JSON.parse(tDesign("includes").replace(/'/g, '"'));

  return [
    {
      title: tAutomatization("title"),
      description: tAutomatization("description"),
      price: tAutomatization("price"),
      img: automatization,
      includes: automatizationArray,
    },

    {
      title: tDesign("title"),
      description: tDesign("description"),
      price: tDesign("price"),
      img: design,
      includes: designArray,
    },

    {
      title: tMobile("title"),
      description: tMobile("description"),
      price: tMobile("price"),
      img: mobile,
      includes: mobileArray,
    },
  ];
};
