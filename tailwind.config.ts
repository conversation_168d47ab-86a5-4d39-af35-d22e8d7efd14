import { heroui } from "@heroui/react";
import type { Config } from "tailwindcss";

const config: Config = {
  darkMode: ["class", `[class="dark"]`],
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/modules/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      // Добавляем утилиты для скрытия скроллбара
      utilities: {
        ".scrollbar-hide::-webkit-scrollbar": {
          display: "none",
        },
        ".scrollbar-hide": {
          "-ms-overflow-style": "none",
          "scrollbar-width": "none",
        },
      },
    },
  },
  plugins: [heroui()],
};

export default config;
