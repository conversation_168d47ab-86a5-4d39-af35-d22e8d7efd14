// src/lib/api.ts

import axios, { AxiosInstance } from "axios";

const baseURL = process.env.NEXT_PUBLIC_BASE_URL;
if (!baseURL) {
  throw new Error("Environment variable NEXT_PUBLIC_BASE_URL is not defined");
}

const api: AxiosInstance = axios.create({
  baseURL,
  timeout: 10000,
});

export default api;

export const apiFetch = async <T>(
  url: string,
  options?: RequestInit
): Promise<T> => {
  const res = await fetch(`${baseURL}${url}`, {
    headers: {
      "Content-Type": "application/json",
      ...(options?.headers || {}),
    },
    ...options,
  });

  const bodyText = await res.text(); // читаем 1 раз

  if (!res.ok) {
    throw new Error(`API error ${res.status}: ${res.statusText}\n${bodyText}`);
  }

  try {
    return JSON.parse(bodyText) as T;
  } catch {
    throw new Error(`Failed to parse JSON: ${bodyText}`);
  }
};
