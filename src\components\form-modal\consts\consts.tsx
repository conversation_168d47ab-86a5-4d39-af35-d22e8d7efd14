import { useTranslations } from "next-intl";

export const SERVICES = () => {
  const t = useTranslations("common.services");

  return [
    { id: 1, name: t("automatization") },
    { id: 2, name: t("design") },
    { id: 3, name: t("mobileDev") },
    { id: 4, name: t("landing") },
    { id: 5, name: t("crm") },
    { id: 6, name: t("integration") },
    { id: 7, name: t("blockchain") },
    { id: 8, name: t("seo") },
    { id: 9, name: t("gis") },
    { id: 10, name: t("turnkeys") },
    { id: 10, name: t("other") },
  ];
};
