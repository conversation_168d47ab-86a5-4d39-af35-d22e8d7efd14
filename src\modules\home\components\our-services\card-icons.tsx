import Image, { StaticImageData } from "next/image";

type Props = {
  title: string;
  description: string;
  icons: StaticImageData[];
};

const CardIcons = ({ title, description, icons }: Props) => {
  return (
    <div className="flex flex-col gap-5 bg-[#F3F5F8] dark:bg-[#2A2A2A] p-6 rounded-[20px]">
      <div className="flex flex-col gap-3">
        <h4 className="text-[22px] text-[#090909] dark:text-white leading-[110%] font-bold ">
          {title}
        </h4>
        <p className="text-[15px] text-[#090909] dark:text-white leading-[130%] opacity-70 font-normal">
          {description}
        </p>
      </div>
      <div className="relative flex justify-end h-[56px]">
        {icons.map((item, i) => {
          const gap = 37; // шаг смещения в пикселях
          const baseZ = 10; // базовый z-index для последней иконки
          const total = icons.length;

          // Вычисляем смещение: для последнего элемента (i = total - 1) получим 0, для предыдущих — шаг умноженный на количество позиций между ними и последним
          const rightOffset = (total - 1 - i) * gap;
          // Вычисляем z-index: чем левее элемент, тем выше он по слою
          const zIndex = baseZ + (total - 1 - i) * 10;

          return (
            <Image
              blurDataURL={item.blurDataURL}
              key={i}
              src={item}
              alt="Icon"
              className="absolute w-[56px] h-[56px] rounded-full"
              style={{ right: `${rightOffset}px`, zIndex }}
            />
          );
        })}
      </div>
    </div>
  );
};

export default CardIcons;
