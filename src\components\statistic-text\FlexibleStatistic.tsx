import React from 'react';
import { StatisticParser, ParseRule } from './StatisticParser';
import { 
  StatisticStack, 
  StatisticInline, 
  StatisticDescription,
  StatisticPartProps 
} from './StatisticParts';
import { RULE_COMBINATIONS } from './StatisticRules';

export interface FlexibleStatisticProps {
  text: string;
  rules?: ParseRule[];
  layout?: 'stack' | 'inline';
  splitAt?: string | RegExp; // Where to split text for stack layout
  className?: string;
  textClassName?: string;
  highlightClassName?: string;
  children?: React.ReactNode; // For custom composition
}

/**
 * Flexible statistic component using composition pattern
 * Much more maintainable than the previous layout-based approach
 */
export const FlexibleStatistic: React.FC<FlexibleStatisticProps> = ({
  text,
  rules = RULE_COMBINATIONS.GENERAL_STATS,
  layout = 'inline',
  splitAt,
  className,
  textClassName,
  highlightClassName,
  children
}) => {
  // If children provided, use custom composition
  if (children) {
    return <div className={className}>{children}</div>;
  }

  // Handle stack layout with text splitting
  if (layout === 'stack' && splitAt) {
    const parts = typeof splitAt === 'string' 
      ? text.split(splitAt)
      : text.split(splitAt);
    
    if (parts.length >= 2) {
      const [firstPart, ...restParts] = parts;
      const secondPart = restParts.join(typeof splitAt === 'string' ? splitAt : '');
      
      return (
        <StatisticStack className={className}>
          <StatisticParser
            text={firstPart.trim()}
            rules={rules}
            defaultTextClassName={textClassName}
            defaultHighlightClassName={highlightClassName}
          />
          {secondPart.trim() && (
            <StatisticDescription className={textClassName}>
              {secondPart.trim()}
            </StatisticDescription>
          )}
        </StatisticStack>
      );
    }
  }

  // Handle inline layout or fallback
  const Container = layout === 'stack' ? StatisticStack : StatisticInline;
  
  return (
    <Container className={className}>
      <StatisticParser
        text={text}
        rules={rules}
        defaultTextClassName={textClassName}
        defaultHighlightClassName={highlightClassName}
      />
    </Container>
  );
};

// Convenience components for common patterns
export const StatisticWithDescription: React.FC<{
  mainText: string;
  description: string;
  rules?: ParseRule[];
  className?: string;
}> = ({ mainText, description, rules, className }) => (
  <StatisticStack className={className}>
    <StatisticParser
      text={mainText}
      rules={rules || RULE_COMBINATIONS.GENERAL_STATS}
    />
    <StatisticDescription>{description}</StatisticDescription>
  </StatisticStack>
);

export const StatisticInlineWithPrefix: React.FC<{
  prefix: string;
  highlight: string;
  suffix?: string;
  description?: string;
  className?: string;
}> = ({ prefix, highlight, suffix, description, className }) => (
  <StatisticStack className={className}>
    <StatisticInline>
      {prefix && <span className="font-medium md:text-[16px] text-[14px] leading-[130%]">{prefix} </span>}
      <span className="md:text-[64px] text-[40px] md:leading-[50px] leading-[30px] font-semibold">
        {highlight}
      </span>
      {suffix && <span className="font-medium md:text-[16px] text-[14px] leading-[130%]"> {suffix}</span>}
    </StatisticInline>
    {description && (
      <StatisticDescription>{description}</StatisticDescription>
    )}
  </StatisticStack>
);
