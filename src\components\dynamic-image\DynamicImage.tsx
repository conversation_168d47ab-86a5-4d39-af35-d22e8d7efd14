import { getImage } from "@/shared/functions/getImage";
import Image from "next/image";

interface Props {
  url: string;
  alt: string;
  containerClassName?: string;
  imageClassName?: string;
}

const DynamicImage = async ({
  url,
  alt,
  containerClassName,
  imageClassName,
}: Props) => {
  const { base64, img } = await getImage(url);
  return (
    <div className={`relative ${containerClassName}`}>
      <Image
        {...img}
        alt={alt}
        placeholder="blur"
        blurDataURL={base64}
        className={imageClassName}
      />
    </div>
  );
};

export default DynamicImage;
