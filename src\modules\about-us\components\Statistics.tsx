import Image from "next/image";
import { useTranslations } from "next-intl";
import companies from "@about-us/assets/statistics/companies.svg";
import image from "@about-us/assets/statistics/image.svg";
import shape from "@about-us/assets/statistics/shape.svg";
import logo from "@shared/assets/logo.svg";

const Statistics = () => {
  const t = useTranslations("aboutUs.ourStatictics");
  return (
    <div className="lg:px-[50px] px-5 flex flex-col lg:gap-[40px] gap-[30px]">
      <div className="flex flex-col items-end md:gap-[60px] gap-4">
        <h3 className="lg:pl-[200px] font-medium md:text-[44px] text-[26px] md:leading-[50px] leading-[24px]">
          {t("title")}
        </h3>
        <p className="max-w-[380px] md:text-[18px] text-[16px] md:leading-[130%] leading-[110%]">
          {t("description")}
        </p>
      </div>

      <div className="grid lg:grid-cols-4 sm:grid-cols-2 grid-cols-1 gap-4">
        <div className="p-[30px] lg:min-h-[350px] min-h-[300px] bg-[#F3F5F8] dark:bg-[#2A2A2A] rounded-[25px] flex flex-col justify-between">
          <div className="flex flex-col gap-[10px]">
            <h5 className="text-[36px] font-semibold leading-[100%]">
              {t("statisctics.partnerships.number")}
            </h5>
            <p className="text-[16px] font-medium leading-[130%]">
              {t("statisctics.partnerships.text")}
            </p>
          </div>
          <Image src={companies} alt="Companes" />
        </div>
        <div className="p-[30px] lg:min-h-[350px] min-h-[300px] bg-[#F3F5F8] dark:bg-[#2A2A2A] rounded-[25px] flex flex-col items-end justify-between">
          <Image src={shape} alt="Shapes" />

          <div className="flex flex-col gap-[10px]">
            <h5 className="text-[36px] font-semibold leading-[100%]">
              {t("statisctics.events.number")}
            </h5>
            <p className="text-[16px] font-medium leading-[130%]">
              {t("statisctics.events.text")}
            </p>
          </div>
        </div>
        <Image
          src={image}
          alt="Image"
          className="rounded-[145px] object-cover w-full lg:max-h-[350px] max-h-[300px]"
        />
        <div className="p-[30px] lg:min-h-[350px] min-h-[300px] bg-[#F3F5F8] dark:bg-[#2A2A2A] rounded-[25px] flex flex-col justify-between">
          <div className="flex justify-end">
            <div className="px-[16px] py-[10px] rounded-[26px] border-[#181818] border-[1px] border-solid flex items-center gap-[10px]">
              <Image src={logo} alt="Logo" />
              <span className="text-[18px] font-semibold leading-[100%]">
                NexLink
              </span>
            </div>
          </div>
          <div className="flex flex-col gap-[10px]">
            <h5 className="text-[36px] font-semibold leading-[100%]">
              {t("statisctics.clients.number")}
            </h5>
            <p className="text-[16px] font-medium leading-[130%]">
              {t("statisctics.clients.text")}
            </p>
          </div>

          <span className="font-bold text-[16px] leading-[130%]">
            Nexlink.pro
          </span>
        </div>
      </div>
    </div>
  );
};

export default Statistics;
