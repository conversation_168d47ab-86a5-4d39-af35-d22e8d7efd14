"use client";

import image1 from "@home/assets/about-company/1.svg";
import image3 from "@home/assets/about-company/3.svg";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { TEAM } from "./consts";
import { useTheme } from "next-themes";

export const STATISTICS = () => {
  const t = useTranslations("home.aboutCompany.statistics");
  const { theme } = useTheme();

  return [
    {
      img: image1,
      title: t("uptime.title"),
      description: t("uptime.description"),
    },
    {
      img: TEAM[theme ? theme : "light"].map((item, i, array) => {
        const gap = 37; // шаг смещения в пикселях
        const baseZ = 10; // базовый z-index для последней иконки
        const total = array.length;

        // Вычисляем смещение: для последнего элемента (i = total - 1) получим 0, для предыдущих — шаг умноженный на количество позиций между ними и последним
        const rightOffset = (total - 1 - i) * gap;
        // Вычисляем z-index: чем левее элемент, тем выше он по слою
        const zIndex = baseZ + (total - 1 - i) + 1;

        return (
          <Image
            placeholder="blur"
            key={i}
            src={item}
            alt="Icon"
            className="absolute w-[56px] h-[56px] rounded-full object-cover object-top border-[3px] border-solid border-white"
            style={{ right: `${rightOffset}px`, zIndex }}
          />
        );
      }),
      title: t("teamCount.title"),
      description: t("teamCount.description"),
    },
    {
      img: image3,
      title: t("projectCount.title"),
      description: t("projectCount.description"),
    },
    {
      img: image3,
      title: t("userCount.title"),
      description: t("userCount.description"),
      isLast: true,
    },
  ];
};
