import React from "react";

interface StatisticTextProps {
  text: string;
  className?: string;
  numberClassName?: string;
}

/**
 * Компонент для отображения статистического текста с выделенными числами
 * Автоматически находит числа в тексте и делает их жирными и крупными
 */
const StatisticText: React.FC<StatisticTextProps> = ({
  text,
  className = "",
  numberClassName,
}) => {
  // Регулярное выражение для поиска чисел (включая десятичные, проценты, плюсы и т.д.)
  const numberRegex =
    /(\d+(?:[.,]\d+)?(?:\s*(?:[+%]|из\s+\d+|out\s+of\s+\d+))?)/gi;

  const parts = text.split(numberRegex);

  return (
    <span className={`text-[16px] leading-[130%] font-medium ${className}`}>
      {parts.map((part, index) => {
        // Проверяем, является ли часть числом
        if (numberRegex.test(part)) {
          return (
            <strong
              key={index}
              className={`md:text-[64px] text-[40px] leading-[100%] font-semibold ${numberClassName}`}
            >
              {part}
            </strong>
          );
        }
        return part;
      })}
    </span>
  );
};

export default StatisticText;
