import React from "react";

export type StatisticLayout =
  | "number-bottom" // Number at bottom, description above
  | "number-inline" // Number inline with text
  | "number-top" // Number at top, description below
  | "highlight-words"; // Highlight specific words (not just numbers)

export interface StatisticTextProps {
  text: string;
  layout?: StatisticLayout;
  className?: string;
  numberClassName?: string;
  descriptionClassName?: string;
  containerClassName?: string;
  highlightWords?: string[]; // For highlighting specific words
}

/**
 * Компонент для отображения статистического текста с различными макетами
 * Поддерживает разные способы выделения чисел и слов
 */
const StatisticText: React.FC<StatisticTextProps> = ({
  text,
  layout = "number-inline",
  className = "",
  numberClassName = "md:text-[64px] text-[40px] md:leading-[50px] leading-[30px] font-semibold",
  descriptionClassName = "font-medium md:text-[16px] text-[14px] leading-[130%]",
  containerClassName = "",
  highlightWords = [],
}) => {
  // Enhanced regex for numbers including decimals, percentages, and phrases
  const numberRegex =
    /(\d+(?:[.,]\d+)?(?:\s*(?:[+%]|из\s+\d+|out\s+of\s+\d+))?)/gi;

  const renderContent = () => {
    let parts: string[];
    let testRegex: RegExp;

    if (layout === "highlight-words" && highlightWords.length > 0) {
      // Create regex for highlighting specific words
      const wordsPattern = highlightWords
        .map((word) => word.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"))
        .join("|");
      testRegex = new RegExp(`(${wordsPattern})`, "gi");
      parts = text.split(testRegex);
    } else {
      // Use number regex
      testRegex = numberRegex;
      parts = text.split(numberRegex);
    }

    return parts.map((part, index) => {
      const shouldHighlight =
        layout === "highlight-words"
          ? highlightWords.some(
              (word) => word.toLowerCase() === part.toLowerCase()
            )
          : testRegex.test(part);

      if (shouldHighlight) {
        console.log(part);
        return (
          <span key={index} className={numberClassName}>
            {part}
          </span>
        );
      }
      return part;
    });
  };

  const content = renderContent();

  // Handle different layouts
  switch (layout) {
    case "number-bottom":
      // Split content into number and description parts
      const numberParts = content.filter(
        (part) =>
          typeof part === "object" ||
          (typeof part === "string" && numberRegex.test(part))
      );
      const descParts = content
        .filter((part) => typeof part === "string" && !numberRegex.test(part))
        .join("")
        .trim();

      return (
        <div
          className={`flex flex-col md:gap-5 gap-[18px] ${containerClassName}`}
        >
          {descParts && <p className={descriptionClassName}>{descParts}</p>}
          <h4 className={numberClassName}>{numberParts}</h4>
        </div>
      );

    case "number-top":
      const topNumberParts = content.filter(
        (part) =>
          typeof part === "object" ||
          (typeof part === "string" && numberRegex.test(part))
      );
      const topDescParts = content
        .filter((part) => typeof part === "string" && !numberRegex.test(part))
        .join("")
        .trim();

      return (
        <div
          className={`flex flex-col md:gap-5 gap-[18px] ${containerClassName}`}
        >
          <h4 className={numberClassName}>{topNumberParts}</h4>
          {topDescParts && (
            <p className={descriptionClassName}>{topDescParts}</p>
          )}
        </div>
      );

    case "number-inline":
    case "highlight-words":
    default:
      return (
        <h4
          className={`${descriptionClassName} flex items-start flex-wrap md:gap-3 gap-[6px] ${containerClassName} ${className}`}
        >
          {content}
        </h4>
      );
  }
};

export default StatisticText;
