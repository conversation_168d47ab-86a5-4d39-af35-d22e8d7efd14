"use client";

import { useCheckMount } from "@/shared/hooks/useCheckMount";
import { useTheme } from "next-themes";

const VK = () => {
  const { theme } = useTheme();
  const isMounted = useCheckMount();

  if (!isMounted) return null;

  return (
    <svg
      width="41"
      height="41"
      viewBox="0 0 41 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="1"
        y="1"
        width="39"
        height="39"
        rx="19.5"
        stroke={theme === "dark" ? "#ffffff" : "#181818"}
      />
      <path
        d="M21.4825 27.5C13.966 27.5 9.67864 22.2447 9.5 13.5H13.2651C13.3888 19.9184 16.1646 22.6371 18.3632 23.1977V13.5H21.9085V19.0355C24.0796 18.7973 26.3607 16.2748 27.1302 13.5H30.6755C30.3855 14.9391 29.8075 16.3017 28.9776 17.5025C28.1477 18.7033 27.0837 19.7166 25.8523 20.479C27.2269 21.1756 28.441 22.1615 29.4145 23.3717C30.388 24.582 31.0988 25.989 31.5 27.5H27.5974C27.2373 26.1876 26.5054 25.0128 25.4934 24.1228C24.4814 23.2328 23.2343 22.6673 21.9085 22.497V27.5H21.4825Z"
        fill={theme === "dark" ? "#ffffff" : "#181818"}
      />
    </svg>
  );
};

export default VK;
