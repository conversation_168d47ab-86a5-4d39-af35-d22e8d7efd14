import light from "../assets/light.svg";
import dark from "../assets/dark.svg";
import { useTheme } from "next-themes";
import Image from "next/image";
import { useCheckMount } from "@/shared/hooks/useCheckMount";

const ChangeTheme = () => {
  const { setTheme, theme } = useTheme();
  const isMounted = useCheckMount();

  if (!isMounted) return null;

  const toggleTheme = () => {
    if (theme === "dark") {
      setTheme("light");
      document.cookie = `theme=light; path=/; max-age=31536000`;
    } else if (theme === "light") {
      setTheme("dark");
      document.cookie = `theme=dark; path=/; max-age=31536000`;
    } else {
      setTheme("light");
      document.cookie = `theme=light; path=/; max-age=31536000`;
    }
  };

  return (
    <button
      onClick={toggleTheme}
      className={`bg-[#78788029] p-[2px] flex items-center ${
        theme === "light" ? "justify-start" : "justify-end"
      } rounded-full w-[51px] transition-all`}
    >
      <span className="w-[27px] h-[27px] bg-white rounded-full flex items-center justify-center">
        {theme === "light" ? (
          <Image src={light} alt="Light Mode" />
        ) : (
          <Image src={dark} alt="Dark Mode" />
        )}
      </span>
    </button>
  );
};

export default ChangeTheme;
