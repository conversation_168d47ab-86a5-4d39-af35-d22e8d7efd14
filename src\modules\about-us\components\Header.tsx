import { setImageToText } from "@/shared/functions/setImageToText";
import title1 from "@about-us/assets/shapes/title1.svg";
import title2 from "@about-us/assets/shapes/title2.svg";
import Typography from "@/components/typography";
import { getGalleryByName } from "@/shared/api/getGalleryByName";
import { getTranslations } from "next-intl/server";
import DynamicImage from "@/components/dynamic-image";

const Header = async () => {
  const t = await getTranslations("aboutUs");

  const data = (await getGalleryByName("about-us")) ?? [];

  return (
    <div className="flex flex-col items-center gap-[40px] md:pt-[214px] pt-[169px] md:pb-[180px] pb-[100px]">
      <div className="flex flex-col md:gap-5 gap-4 max-w-[793px] text-center">
        <Typography variant="h2">
          {setImageToText(
            t("title"),
            ["технологии,", "развитие"],
            [title1, title2],
            [
              { width: 76, height: 76 },
              { width: 76, height: 76 },
            ]
          )}
        </Typography>
        <Typography variant="p">{t("description")}</Typography>
      </div>
      <div className="w-full flex md:gap-4 gap-[10px] overflow-x-auto scrollbar-hide px-5">
        {data.map((item, index) => (
          <DynamicImage
            key={item}
            url={item}
            alt={`Team Picture ${index + 1}`}
            imageClassName="md:min-w-[550px] md:h-[360px] min-w-[340px] h-[222px] rounded-[20px] object-cover"
          />
        ))}
      </div>
    </div>
  );
};

export default Header;
