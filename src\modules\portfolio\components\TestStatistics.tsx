import StatisticText from "@/components/statistic-text";

const TestStatistics = () => {
  const testCases = [
    {
      title: "Тест 1: 300+ не должно захватывать 3 из '3 месяца'",
      text: "300+ установок мобильного приложения за первые 3 месяца",
      layout: "number-bottom" as const,
    },
    {
      title: "Тест 2: Более 200+ сверху, описание снизу",
      text: "Более 200+ объектов размещения по всему Кыргызстану",
      layout: "number-with-prefix-top" as const,
    },
    {
      title: "Тест 3: 4.8 из 5 должно выделиться полностью",
      text: "4.8 из 5 Уровень удовлетворённости пользователей (по опросу)",
      layout: "number-bottom" as const,
    },
    {
      title: "Тест 4: Выделение слова 'туры'",
      text: "Ежедневные туры с комфортом и по доступной цене",
      layout: "highlight-words" as const,
      highlightWords: ["туры"],
    },
  ];

  return (
    <div className="space-y-8 p-6">
      <h2 className="text-2xl font-bold">Тестирование StatisticText</h2>
      
      {testCases.map((testCase, index) => (
        <div key={index} className="border p-4 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">{testCase.title}</h3>
          <p className="text-sm text-gray-600 mb-4">
            Исходный текст: "{testCase.text}"
          </p>
          <p className="text-sm text-gray-600 mb-4">
            Layout: {testCase.layout}
          </p>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded">
            <StatisticText
              text={testCase.text}
              layout={testCase.layout}
              highlightWords={testCase.highlightWords}
            />
          </div>
        </div>
      ))}
    </div>
  );
};

export default TestStatistics;
