import Typography from "@/components/typography";
import { setImageToText } from "@/shared/functions/setImageToText";
import { useTranslations } from "next-intl";
import seacoral from "@our-services/assets/shapes/seacoral.svg";
import waves from "@our-services/assets/shapes/waves.svg";

const WeOffer = () => {
  const t = useTranslations("ourServices");
  return (
    <div className="px-5 md:py-[260px] py-[100px] max-w-[1150px] m-auto">
      <Typography variant="h2" className="text-center">
        {setImageToText(
          t("weOffer"),
          ["спектр услуг,", "и потребности."],
          [seacoral, waves],
          [
            { width: 116, height: 116 },
            { width: 101, height: 101 },
          ]
        )}
      </Typography>
    </div>
  );
};

export default WeOffer;
