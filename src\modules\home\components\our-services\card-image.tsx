import Image, { StaticImageData } from "next/image";

type Props = {
  title: string;
  description: string;
  img?: StaticImageData;
  reverse?: boolean;
};

const CardImage = ({ title, description, img, reverse }: Props) => {
  return (
    <div
      className={`flex ${
        reverse ? "flex-col-reverse" : "flex-col"
      } gap-5 bg-[#F3F5F8] dark:bg-[#2A2A2A] p-6 rounded-[20px]`}
    >
      {img ? (
        <Image
          blurDataURL={img.blurDataURL}
          src={img}
          alt="Card Image"
          className="w-full h-[218px] object-cover rounded-[15px]"
        />
      ) : null}

      <div className="flex flex-col gap-3">
        <h4 className="text-[22px] text-[#090909] dark:text-white leading-[110%] font-bold ">
          {title}
        </h4>
        <p className="text-[15px] text-[#090909] dark:text-white leading-[130%] font-normal opacity-70">
          {description}
        </p>
      </div>
    </div>
  );
};

export default CardImage;
