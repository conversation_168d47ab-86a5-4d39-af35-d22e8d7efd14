import { Header } from "@/modules/portfolio/components";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Страница с портфолио NexLinkPro",
};

export default async function PortfolioLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="flex flex-col lg:gap-[110px] md:gap-[60px] gap-[30px] lg:px-[50px] px-5 mb-[100px]">
      <Header />
      {children}
    </div>
  );
}
