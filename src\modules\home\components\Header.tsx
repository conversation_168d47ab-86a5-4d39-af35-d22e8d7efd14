"use client";

import Background from "@/components/backgound";
import OrderButton from "@/components/order-button/OrderButton";
import { useDisclosure } from "@heroui/react";
import FormModal from "@/components/form-modal";
import { useTranslations } from "next-intl";
import { OUR_PRODUCTS } from "../consts/consts";
import Carousel from "./carousel/carousel";

const Header = () => {
  const t = useTranslations("home");
  const { isOpen, onClose, onOpen } = useDisclosure();

  return (
    <div className="relative lg:pt-[170px] pt-[100px] lg:pb-[80px] pb-[30px]">
      <Background />
      <div className="m-auto h-full max-w-[1100px] flex flex-col items-center justify-center gap-[20px] text-center px-5">
        {/* <Image
          className="lg:w-[115px] lg:h-[120px] w-[91px] h-[97px]"
          src={izde}
          alt="Izde Image"
        /> */}

        <Carousel items={OUR_PRODUCTS} />
        <h1 className="lg:text-[70px] md:text-[58px] sm:text-[48px] text-[42px] lg:leading-[89px] md:leading-[77px] sm:leading-[43px] leading-[38px] font-bold">
          {t("title")}
        </h1>
        <p className="lg:text-[22px] text-[16px] leading-[130%] font-normal">
          {t("description")}
        </p>
        <OrderButton onPress={onOpen}>{t("orderDevelopment")}</OrderButton>

        <FormModal isOpen={isOpen} handleClose={onClose} />
      </div>
    </div>
  );
};

export default Header;
