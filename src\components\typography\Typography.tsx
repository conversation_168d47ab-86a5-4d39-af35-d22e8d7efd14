import { TypographyVariantType } from "./types/VariantType";

type VariantMap = {
  as: keyof React.JSX.IntrinsicElements;
  className: React.HTMLAttributes<HTMLHeadingElement>["className"];
};

type Props = {
  variant?: TypographyVariantType;
  children?: React.ReactNode;
  className?: React.HTMLAttributes<HTMLHeadingElement>["className"];
};

const typographyVariants: Record<TypographyVariantType, VariantMap> = {
  h2: {
    as: "h2",
    className:
      "lg:text-[74px] md:text-[54px] text-[27px] lg:leading-[84px] md:leading-[64px] leading-[29px] font-semibold",
  },
  h3: {
    as: "h3",
    className:
      "lg:text-[70px] md:text-[58px] sm:text-[48px] text-[42px] lg:leading-[89px] md:leading-[77px] sm:leading-[43px] leading-[38px] font-bold",
  },
  h4: {
    as: "h4",
    className:
      "lg:text-[44px] md:text-[38px] sm:text-[32px] text-[26px] lg:leading-[52px] md:leading-[46px] leading-[34px] font-bold",
  },
  h5: { as: "h5", className: "text-[32px] leading-[100%] font-semibold" },
  h6: {
    as: "h6",
    className: "lg:text-[18px] text-[16px] leading-[130%] font-semibold",
  },
  p: {
    as: "p",
    className: "lg:text-[18px] text-[16px] leading-[130%] font-medium",
  },
};

const Typography = ({ variant = "p", children, className }: Props) => {
  const { as: Component, className: cls } = typographyVariants[variant];

  return <Component className={`${cls} ${className}`}>{children}</Component>;
};

export default Typography;
