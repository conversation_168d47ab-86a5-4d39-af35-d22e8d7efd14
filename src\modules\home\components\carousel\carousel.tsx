import Image, { StaticImageData } from "next/image";
import Link from "next/link";
import { useCallback, useEffect, useState } from "react";

type Props = {
  items: {
    img: StaticImageData;
    imgStyles: string;
    divStyles: string;
    path: string;
  }[];
};

const ANIM_DURATION = 600; // длительность анимации в мс (0.6 с)
const INTERVAL = 2000;

const Carousel = ({ items }: Props) => {
  const [index, setIndex] = useState(0);
  const [animating, setAnimating] = useState(false);
  const [isFocused, setIsFocused] = useState<boolean>(false);

  // Запускаем анимацию за ANIM_DURATION до смены
  useEffect(() => {
    if (isFocused) return;
    const timer = setTimeout(() => {
      setAnimating(true);
    }, INTERVAL - ANIM_DURATION);
    return () => clearTimeout(timer);
  }, [index, isFocused]);

  const handleAnimationEnd = useCallback(() => {
    if (isFocused) return;
    // сбрасываем анимацию и меняем индекс
    setAnimating(false);
    setIndex((i) => (i + 1) % items.length);
  }, []);

  // Вычисляем тройку позиций по текущему индексу
  const positions = [0, 1, 2].map(
    (offset) => items[(index + offset) % items.length]
  );

  return (
    <div className="relative w-[115px] h-[130px]">
      {positions.reverse().map((item, i) => {
        return (
          <Link
            onMouseEnter={() => setIsFocused(true)}
            onMouseLeave={() => setIsFocused(false)}
            href={item.path}
            key={item.img.src}
            onAnimationEnd={
              i === 2 && animating ? handleAnimationEnd : undefined
            }
            className={`
            absolute left-0 md:w-[115px] md:h-[106px] w-[100px] h-[97px] bg-[#F3F5F8]
            flex items-center justify-center rounded-[24px]
            ${i === 0 ? "top-0" : i === 1 ? "top-[14px]" : "top-[28px]"}
            ${animating ? "top-[28px]" : ""}
            ${i === 2 && animating ? "animate-scale-fade-once" : ""}
           ${item.divStyles}
              transition-all duration-500 ease-in-out
              drop-shadow-md
              ${
                positions.length - 1 === i
                  ? "hover:scale-125 cursor-pointer"
                  : ""
              }
           `}
          >
            <Image src={item.img} alt="Product" className={item.imgStyles} />
          </Link>
        );
      })}
    </div>
  );
};

export default Carousel;
