import Companies from "./Companies";
import { getServerTheme } from "@/shared/functions/getServerTheme";
import { getGalleryByName } from "@/shared/api/getGalleryByName";
import DynamicImage from "@/components/dynamic-image";

const TrustUs = async () => {
  const theme = await getServerTheme();
  const homeGallery = (await getGalleryByName("home")) ?? [];

  console.log("homeGallery", homeGallery);
  return (
    <div className="flex flex-col gap-[100px]">
      <div className="flex flex-col items-center gap-5 px-5">
        <h3 className="text-[16px] leading-[130%] font-normal text-[#181818] dark:text-[#ffffff]">
          Нам доверяют
        </h3>
        <Companies serverTheme={theme} />
      </div>

      <div className="flex gap-4 overflow-x-auto overflow-hidden bar [&::-webkit-scrollbar]:hidden px-5">
        {homeGallery.map((item, i) => (
          <DynamicImage
            key={i}
            imageClassName="lg:min-w-[550px] lg:h-[360px] min-w-[340px] h-[222px] object-cover rounded-[30px]"
            url={item}
            alt="Image"
          />
        ))}
      </div>
    </div>
  );
};

export default TrustUs;
