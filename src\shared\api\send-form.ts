import { AxiosError } from "axios";
import api from "@/shared/api-config/axios-config";
import { FeedbackPayload } from "../types";

const nameRegex = /^[А-Яа-яA-Za-zёЁ\- ]{2,30}$/;
const emailRegex = /^[\w.-]+@[\w.-]+\.\w{2,}$/;
const phoneRegex = /^\+?\d{10,15}$/;

export const sendForm = async (
  payload: FeedbackPayload & { offerings?: string[] }
): Promise<{ status: "error" | "success"; message: string }> => {
  // 1. Проверяем, что payload вообще передали
  if (!payload) {
    return { status: "error", message: "Заполните все необходимые поля." };
  }

  const { name, email, phone_number, message } = payload;

  // 2. Имя — обязательно и по шаблону
  if (!name.trim() || !nameRegex.test(name.trim())) {
    return {
      status: "error",
      message:
        "Пожалуйста, введите корректное имя (только буквы, пробелы и дефисы, от 2 до 30 символов).",
    };
  }

  // 3. Сообщение — обязательно
  if (!message.trim()) {
    return { status: "error", message: "Пожалуйста, введите сообщение." };
  }

  // 4. Должен быть заполнен хотя бы E-mail или телефон
  if (!(email?.trim() || phone_number?.trim())) {
    return {
      status: "error",
      message:
        "Пожалуйста, укажите E-mail или номер телефона для обратной связи.",
    };
  }

  // 5. Если указан E-mail — проверяем его
  if (email?.trim() && !emailRegex.test(email.trim())) {
    return {
      status: "error",
      message: "Введите корректный e-mail, например: <EMAIL>.",
    };
  }

  // 6. Если указан телефон — проверяем по шаблону
  if (phone_number?.trim() && !phoneRegex.test(phone_number.trim())) {
    return {
      status: "error",
      message:
        "Введите номер телефона в международном формате, например: +996999123456.",
    };
  }

  // Всё ок — шлём на сервер
  try {
    await api.post("/feedback/send", payload);
    return {
      status: "success",
      message: "Форма успешно отправлена!",
    };
  } catch (error) {
    if (error instanceof AxiosError) {
      return { status: "error", message: error.message };
    }
    return { status: "error", message: "Произошла непредвиденная ошибка." };
  }
};
