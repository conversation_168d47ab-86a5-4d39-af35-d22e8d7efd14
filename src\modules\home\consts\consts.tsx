import aida_dark from "../assets/team/dark/aida_dark.webp";
import aida_light from "../assets/team/light/aida_light.webp";
import kalys_dark from "../assets/team/dark/kalys_dark.webp";
import kalys_light from "../assets/team/light/kalys_light.webp";
import nursultan_dark from "../assets/team/dark/nursultan_dark.webp";
import nursultan_light from "../assets/team/light/nursultan_light.webp";
import { StaticImageData } from "next/image";

import izde from "../assets/our-projects/izde.svg";
import izde_business from "../assets/our-projects/izde_business.svg";
import izdesim from "../assets/our-projects/izdesim.svg";

export const TEAM: Record<string, StaticImageData[]> = {
  dark: [aida_dark, kalys_dark, nursultan_dark],
  light: [aida_light, kalys_light, nursultan_light],
};

export const OUR_PRODUCTS = [
  {
    img: izde,
    imgStyles: "w-[74px] h-[34px]",
    divStyles: "",
    path: "/our-works/izde",
  },
  {
    img: izde_business,
    imgStyles: "w-full h-full object-cover",
    divStyles: "overflow-hidden",
    path: "/our-works/izde#izde-business",
  },
  {
    img: izdesim,
    imgStyles: "w-[70px] h-[40px] md:w-[87px] md:h-[53px]",
    divStyles: "",
    path: "/our-works/izde-sim",
  },
];
