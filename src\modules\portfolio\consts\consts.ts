import { useTranslations } from "next-intl";
import { ParseRule } from "@/components/statistic-text/StatisticParser";

export type StatisticType =
  | "auto" // Auto-detect numbers and highlight
  | "split" // Split text at a point
  | "inline-prefix" // Prefix + number inline
  | "word-highlight" // Highlight specific words
  | "custom"; // Custom composition

export interface StatisticConfig {
  type: StatisticType;
  text?: string;
  prefix?: string;
  highlight?: string;
  suffix?: string;
  description?: string;
  splitAt?: string | RegExp;
  highlightWords?: string[];
  rules?: ParseRule[];
  hasIcon?: boolean;
  iconPosition?: "top" | "bottom" | "center";
}

export interface ProjectStatistics {
  first: StatisticConfig;
  second: StatisticConfig;
  third: StatisticConfig;
}

export const PORTFOLIO_STATISTICS = () => {
  return {
    izde: {
      // Example 1 pattern - matches ExampleStatistics1
      first: {
        type: "split" as StatisticType,
        text: "300+ установок мобильного приложения за первые 3 месяца",
        splitAt: "300+",
        hasIcon: true,
        iconPosition: "top" as const,
      },
      second: {
        type: "inline-prefix" as StatisticType,
        prefix: "Более",
        highlight: "200+",
        description: "объектов размещения по всему Кыргызстану",
      },
      third: {
        type: "split" as StatisticType,
        text: "4.8 из 5 Уровень удовлетворённости пользователей (по опросу)",
        splitAt: /(\d+(?:[.,]\d+)?\s+из\s+\d+)/,
        hasIcon: true,
        iconPosition: "top" as const,
      },
    } as ProjectStatistics,

    izdesim: {
      // Example 2 pattern - matches ExampleStatistics2
      first: {
        type: "inline-prefix" as StatisticType,
        prefix: "Более",
        highlight: "200+",
        description: "довольных клиентов, уже доверивших нам своё приключение",
        hasIcon: true,
        iconPosition: "top" as const,
      },
      second: {
        type: "inline-prefix" as StatisticType,
        prefix: "Более",
        highlight: "15",
        description: "направлений природных экскурсий и не только!",
      },
      third: {
        type: "word-highlight" as StatisticType,
        text: "Ежедневные туры с комфортом и по доступной цене",
        highlightWords: ["туры"],
      },
    } as ProjectStatistics,

    izdetour: {
      // Mixed pattern example
      first: {
        type: "auto" as StatisticType,
        text: "99.9% аптайм сервиса",
      },
      second: {
        type: "inline-prefix" as StatisticType,
        prefix: "Более",
        highlight: "1000+",
        description: "активных пользователей ежедневно",
      },
      third: {
        type: "word-highlight" as StatisticType,
        text: "24/7 поддержка клиентов",
        highlightWords: ["24/7"],
      },
    } as ProjectStatistics,
  };
};

export const PRODUCTS = () => {
  const t = useTranslations();
  return {
    izde: {
      common: {
        title: "",
        description: "",
      },
      projects: [
        {
          title: t("projects.izde.title"),
        },
      ],
    },
    izdesim: {
      common: null,
      projects: [],
    },
    izdetour: {
      common: null,
      projects: [],
    },
  };
};
