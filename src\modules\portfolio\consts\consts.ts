import { useTranslations } from "next-intl";
import izde_logo from "@portfolio/assets/logos/izde-logo.svg";
import izde_business_logo from "@portfolio/assets/logos/izde_business-logo.svg";
import izde_moderator_logo from "@portfolio/assets/logos/izde_moderator-logo.svg";
import izdesim_logo from "@portfolio/assets/logos/izdesim-logo.svg";
import izdetour_logo from "@portfolio/assets/logos/izdetour-logo.svg";

export const PRODUCTS = () => {
  const t = useTranslations();
  return {
    izde: {
      common: {
        title: "",
        description: "",
      },
      projects: [
        {
          title: t("projects.izde.title"),
        },
      ],
    },
    izdesim: {
      common: null,
      projects: [],
    },
    izdetour: {
      common: null,
      projects: [],
    },
  };
};
