import { useTranslations } from "next-intl";
import { StatisticLayout } from "@/components/statistic-text/StatisticText";

export interface StatisticItem {
  text: string;
  layout: StatisticLayout;
  highlightWords?: string[];
  hasIcon?: boolean;
  iconPosition?: "top" | "bottom" | "center";
  customStyles?: {
    numberClassName?: string;
    descriptionClassName?: string;
    containerClassName?: string;
  };
}

export interface ProjectStatistics {
  first: StatisticItem;
  second: StatisticItem;
  third: StatisticItem;
}

export const PORTFOLIO_STATISTICS = () => {
  return {
    izde: {
      // Example 1 pattern - matches ExampleStatistics1
      first: {
        text: "300+ установок мобильного приложения за первые 3 месяца",
        layout: "number-bottom" as StatisticLayout,
        hasIcon: true,
        iconPosition: "top" as const,
      },
      second: {
        text: "Более 200+ объектов размещения по всему Кыргызстану",
        layout: "number-with-prefix-top" as StatisticLayout,
      },
      third: {
        text: "4.8 из 5 Уровень удовлетворённости пользователей (по опросу)",
        layout: "number-bottom" as StatisticLayout,
        hasIcon: true,
        iconPosition: "top" as const,
      },
    } as ProjectStatistics,

    izdetour: {
      // Example 2 pattern - matches ExampleStatistics2
      first: {
        text: "Более 200+ довольных клиентов, уже доверивших нам своё приключение",
        layout: "number-with-prefix-top" as StatisticLayout,
        hasIcon: true,
        iconPosition: "top" as const,
      },
      second: {
        text: "Более 15 направлений природных экскурсий и не только!",
        layout: "number-with-prefix-top" as StatisticLayout,
      },
      third: {
        text: "Ежедневные туры с комфортом и по доступной цене",
        layout: "highlight-words" as StatisticLayout,
        highlightWords: ["туры"],
      },
    } as ProjectStatistics,
  };
};

export const PRODUCTS = () => {
  const t = useTranslations();
  return {
    izde: {
      common: {
        title: "",
        description: "",
      },
      projects: [
        {
          title: t("projects.izde.title"),
        },
      ],
    },
    izdesim: {
      common: null,
      projects: [],
    },
    izdetour: {
      common: null,
      projects: [],
    },
  };
};
