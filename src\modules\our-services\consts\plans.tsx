import { useTranslations } from "next-intl";

export const PLANS = () => {
  const tBase = useTranslations("ourServices.plans.list.base");
  const tScalable = useTranslations("ourServices.plans.list.scalable");
  const tCorporative = useTranslations("ourServices.plans.list.corporative");

  const baseArray = JSON.parse(tBase("includes").replace(/'/g, '"'));

  const scalableArray = JSON.parse(tScalable("includes").replace(/'/g, '"'));

  const corporativeArray = JSON.parse(
    tCorporative("includes").replace(/'/g, '"')
  );

  return [
    {
      title: tBase("title"),
      description: tBase("description"),
      includes: baseArray,
    },

    {
      title: tScalable("title"),
      description: tScalable("description"),
      includes: scalableArray,
    },

    {
      title: tCorporative("title"),
      description: tCorporative("description"),
      includes: corporativeArray,
    },
  ];
};
