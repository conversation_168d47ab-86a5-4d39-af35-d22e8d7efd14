import { useTranslations } from "next-intl";
import { setImageToText } from "@/shared/functions/setImageToText";
import sphere from "@home/assets/fresh-desicions/sphere.svg";
import cube from "@home/assets/fresh-desicions/cube.svg";
import Typography from "@/components/typography";

type Props = {
  locale: string;
};

const FreshDecisions = ({}: Props) => {
  const t = useTranslations("home");
  return (
    <div className="lg:py-[260px] md:py-[160px] py-[100px] px-5 max-w-[1200px] m-auto">
      <Typography variant="h2" className="text-center">
        {setImageToText(
          t("weCreate"),
          ["Создаем", "дня."],
          [sphere, cube],
          [
            { width: 81, height: 83 },
            { width: 76, height: 76 },
          ]
        )}
      </Typography>
    </div>
  );
};

export default FreshDecisions;
