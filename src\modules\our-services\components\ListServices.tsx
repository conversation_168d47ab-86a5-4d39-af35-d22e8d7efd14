import Image from "next/image";
import { LIST_SERVICES } from "../consts/list-services";

const ListServices = () => {
  return (
    <div className="flex flex-col gap-[70px] lg:px-[50px] px-5">
      {LIST_SERVICES().map((item) => (
        <div key={item.title} className="flex flex-col gap-[26px]">
          <div className="flex justify-between items-center gap-2">
            <h5 className="md:text-[29px] text-[26px] leading-[110%] font-semibold">
              {item.title}
            </h5>
            <span className="md:inline-block hidden px-6 py-[10px] border-solid border-[1px] border-[#B64DFF] dark:border-[#CA7EFF] lg:text-[46px] text-[32px] font-bold leading-[100%] text-[#B64DFF] dark:text-[#CA7EFF] rounded-[200px]">
              {item.price}
            </span>
          </div>

          <div className="grid md:grid-cols-[1fr_1.4fr] grid-cols-1 gap-[40px]">
            <Image
              className="md:h-[291px] h-[250px] w-full object-cover rounded-[20px]"
              src={item.img}
              alt="Service Image"
            />
            <div className="flex flex-col gap-[30px]">
              <div className="flex flex-col gap-[10px]">
                {item.includes.map((item: string) => (
                  <p
                    key={item}
                    className="md:text-[18px] text-[16px] font-medium py-[10px] border-b-[1px] border-b-[#1818181A] dark:border-b-[#FFFFFF1A]"
                  >
                    {item}
                  </p>
                ))}
              </div>
              <p className="md:text-[18px] text-[17px] font-medium leading-[22px]">
                {item.description}
              </p>

              <div className="flex justify-end">
                <span className="md:hidden flex w-fit px-[18px] py-[7.5px] border-solid border-[1px] border-[#B64DFF] text-[33px] font-bold leading-[100%] text-[#B64DFF] rounded-[200px]">
                  {item.price}
                </span>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ListServices;
