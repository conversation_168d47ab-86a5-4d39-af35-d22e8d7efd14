import Image, { StaticImageData } from "next/image";
import React from "react";

type Props = {
  title: string;
  description: string;
  img?: StaticImageData;
  price?: string;
  className?: string;
  isMain?: boolean;
};

const Card = ({ className, title, description, img, price, isMain }: Props) => {
  return (
    <div
      className={`grid ${
        isMain
          ? "lg:grid-cols-[1.15fr_1fr] md:grid-cols-[1fr_1fr] grid-cols-1"
          : "grid-cols-1"
      } gap-[30px] ${
        isMain ? "justify-between" : "flex-col"
      } bg-[#F5F5F5] dark:bg-[#2A2A2A] md:p-[30px] p-4 rounded-[30px] ${className}`}
    >
      <div className="flex flex-col gap-[30px] justify-between">
        <div className="flex justify-between items-center gap-2 flex-wrap">
          <h5 className="md:text-[28px] text-[26px] font-semibold leading-[110%]">
            {title}
          </h5>
          {price ? (
            <span className="md:inline-block hidden px-6 py-[10px] border-solid border-[1px] border-[#B64DFF] dark:border-[#CA7EFF]  text-[32px] font-bold leading-[100%] text-[#B64DFF] dark:text-[#CA7EFF] rounded-[200px]">
              {price}
            </span>
          ) : null}
        </div>
        <p className="md:text-[18px] text-[16px] text-normal leading-[22px]">
          {description}
        </p>
      </div>
      {img ? (
        <Image
          className={`md:h-[310px] h-[218px] w-full object-cover rounded-[20px]`}
          src={img}
          alt="Service Image"
        />
      ) : null}
      {price && (
        <span className="md:hidden flex w-fit px-[18px] py-[7.5px] border-solid border-[1px] border-[#B64DFF] text-[28px] font-bold leading-[100%] text-[#B64DFF] rounded-[200px]">
          {price}
        </span>
      )}
    </div>
  );
};

export default Card;
