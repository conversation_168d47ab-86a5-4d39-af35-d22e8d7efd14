import Image from "next/image";
import StatisticText from "@/components/statistic-text";
import { PORTFOLIO_STATISTICS, StatisticItem } from "@portfolio/consts/consts";
import statisticImage from "@portfolio/assets/statistic_image.svg";
import statisticRating from "@portfolio/assets/statistic_rating.svg";
import statisticShape from "@portfolio/assets/statistic_shape.svg";

interface StatisticsProps {
  projectKey: keyof ReturnType<typeof PORTFOLIO_STATISTICS>; // "izde" | "izdesim" | "izdetour"
}

const Statistics = ({ projectKey }: StatisticsProps) => {
  const statisticsData = PORTFOLIO_STATISTICS()[projectKey];

  const renderStatisticCard = (
    statistic: StatisticItem,
    position: "first" | "second" | "third"
  ) => {
    const getIcon = () => {
      if (!statistic.hasIcon) return null;

      switch (position) {
        case "first":
        case "second":
          return statisticShape;
        case "third":
          return statisticRating;
        default:
          return null;
      }
    };

    const icon = getIcon();
    const iconPosition = statistic.iconPosition || "top";

    const renderIcon = () => {
      if (!icon) return null;

      const iconClasses =
        position === "first" || position === "second"
          ? "md:w-[102px] md:h-[92px] w-[64px] h-[58px]"
          : "";

      return <Image src={icon} alt="Icon" className={iconClasses} />;
    };

    const renderContent = () => (
      <StatisticText
        text={statistic.text}
        layout={statistic.layout}
        highlightWords={statistic.highlightWords}
        numberClassName={statistic.customStyles?.numberClassName}
        descriptionClassName={statistic.customStyles?.descriptionClassName}
        containerClassName={statistic.customStyles?.containerClassName}
      />
    );

    // Handle different card layouts based on position and icon placement
    const getCardLayout = () => {
      if (position === "second" && !statistic.hasIcon) {
        // Second card without icon - content only
        return (
          <div className="flex flex-col justify-between md:h-[350px] h-[280px]">
            {renderContent()}
          </div>
        );
      }

      if (iconPosition === "top") {
        return (
          <div className="flex flex-col justify-between items-end md:h-[350px] h-[280px]">
            {renderIcon()}
            {renderContent()}
          </div>
        );
      }

      if (iconPosition === "bottom") {
        return (
          <div className="flex flex-col justify-between md:h-[350px] h-[280px]">
            {renderContent()}
            <div className="flex justify-center">{renderIcon()}</div>
          </div>
        );
      }

      // Default layout
      return (
        <div className="flex flex-col justify-between md:h-[350px] h-[280px]">
          {renderContent()}
        </div>
      );
    };

    return (
      <div className="bg-white dark:bg-[#434343] md:p-[30px] p-[14px] rounded-[25px]">
        {getCardLayout()}
      </div>
    );
  };

  return (
    <div className="grid lg:grid-cols-4 sm:grid-cols-2 grid-cols-1 md:gap-4 gap-[10px]">
      {renderStatisticCard(statisticsData.first, "first")}
      {renderStatisticCard(statisticsData.second, "second")}

      <Image
        src={statisticImage}
        alt="Statistics"
        className="rounded-[145px] w-full md:h-[350px] h-[280px] object-cover"
      />

      {renderStatisticCard(statisticsData.third, "third")}
    </div>
  );
};

export default Statistics;
