import Image from "next/image";
import { useTranslations } from "next-intl";
import StatisticText from "@/components/statistic-text";
import statisticImage from "@portfolio/assets/statistic_image.svg";
import statisticRating from "@portfolio/assets/statistic_rating.svg";
import statisticShape from "@portfolio/assets/statistic_shape.svg";

interface StatisticsProps {
  projectKey: string; // например, "izde"
}

const Statistics = ({ projectKey }: StatisticsProps) => {
  const t = useTranslations(`portfolio.projects.${projectKey}.statistics`);

  return (
    <div className="grid lg:grid-cols-4 md:grid-cols-2 sm:grid-cols-1 gap-4">
      <div className="p-[30px] lg:min-h-[350px] min-h-[300px] bg-[#F3F5F8] dark:bg-[#2A2A2A] rounded-[25px] flex flex-col justify-between">
        <div className="flex justify-end">
          <Image src={statisticShape} alt="Shape" />
        </div>

        <StatisticText
          text={t("first.text")}
          className="text-[16px] font-medium leading-[130%]"
          numberClassName="text-[36px] font-bold block"
        />
      </div>

      {/* Вторая статистика */}
      <div className="p-[30px] lg:min-h-[350px] min-h-[300px] bg-[#F3F5F8] dark:bg-[#2A2A2A] rounded-[25px] flex flex-col items-end justify-between">
        <div className="flex flex-col gap-[10px] w-full">
          <StatisticText
            text={t("second.text")}
            className="text-[16px] font-medium leading-[130%]"
            numberClassName="text-[36px] font-bold block"
          />
        </div>
      </div>

      <Image
        src={statisticImage}
        alt="Statistics"
        className="rounded-[145px] object-cover w-full max-h-full h-full"
      />

      {/* Третья статистика */}
      <div className="p-[30px] lg:min-h-[350px] min-h-[300px] bg-[#F3F5F8] dark:bg-[#2A2A2A] rounded-[25px] flex flex-col justify-between">
        <div className="flex justify-center">
          <Image src={statisticRating} alt="Rating" />
        </div>

        <div className="flex flex-col gap-[10px]">
          <StatisticText
            text={t("third.text")}
            className="text-[16px] font-medium leading-[130%]"
            numberClassName="text-[36px] font-bold block"
          />
        </div>
      </div>
    </div>
  );
};

export default Statistics;
