import Image, { StaticImageData } from "next/image";

type Props = {
  title: string;
  description: string;
  img: StaticImageData;
};

const CardCustom = ({ title, description, img }: Props) => {
  return (
    <div className="flex flex-col gap-5 bg-[#F3F5F8] dark:bg-[#2A2A2A] p-6 pb-0 rounded-[20px]">
      <div className="flex flex-col gap-3">
        <h4 className="text-[22px] text-[#090909] dark:text-white leading-[110%] font-bold ">
          {title}
        </h4>
        <p className="text-[15px] text-[#090909] dark:text-white leading-[130%] opacity-70 font-normal">
          {description}
        </p>
      </div>

      <Image
        blurDataURL={img.blurDataURL}
        src={img}
        alt="Card Image"
        className="w-full object-cover rounded-[15px]"
      />
    </div>
  );
};

export default CardCustom;
