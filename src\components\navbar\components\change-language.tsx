import { useParams } from "next/navigation";
import ChevronDown from "./chevron-down";
import Globe from "@shared/assets/globe";
import { LOCALE, LOCALES_LIST } from "../consts/locales";
import { useState, useTransition } from "react";
import { setUserLocale } from "@/i18n/getUserLocale";
import { Locale } from "@/i18n/config";

const ChangeLanguage = () => {
  const { locale } = useParams<{ locale: string }>();
  const [isChangingLocale, setIsChangingLocale] = useState<boolean>(false);

  const [isPending, startTransition] = useTransition();

  const handleSelectLocale = (newLocale: Locale) => {
    if (isPending) return;

    startTransition(() => {
      setUserLocale(newLocale);
      setIsChangingLocale(false);
    });
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsChangingLocale((prev) => !prev)}
        className="flex items-center gap-2"
      >
        <Globe />
        {LOCALE[locale]}
        <ChevronDown />
      </button>

      {isChangingLocale && (
        <ul className="absolute top-[150%] right-0 bg-white dark:bg-[#2A2A2A] rounded-lg shadow-2xl min-w-[150px] flex flex-col gap-2 overflow-hidden">
          {LOCALES_LIST.map((item) => (
            <li
              key={item.value}
              className="text-[18px] font-medium flex hover:bg-gray-50 dark:hover:bg-[#1A1A1A] transition-all"
            >
              <button
                className="w-full text-start p-2 px-4"
                onClick={() => handleSelectLocale(item.value as Locale)}
              >
                {item.label}
              </button>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default ChangeLanguage;
